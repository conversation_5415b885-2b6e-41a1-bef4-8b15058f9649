<template>
	<view>
		<view class="stepBox">
			<mStepLine :current="current" :activeColor="themeColor.main_color" :list="stepList"></mStepLine>
		</view>
		<!-- 团队信息 -->
		<view class="" v-if="(!user||(user&&!ifAuth))&&teamDetail">
			<view class="teamBox">
				<view class="content">
					<view class=""
						style="display: flex;align-items: center;justify-content: center;font-size: 34rpx;font-weight: 600;">
						<text>{{teamDetail.team_name}}</text>
					</view>
					<view class="title" style="font-size: 28rpx;color: #303133;font-weight: 400;">
						<view class="" style="width: 420rpx;display: flex;align-items: center;">
							<text>联系人:{{teamDetail.linkman}}</text>
							<view @click="makePhone"><uni-icons type="phone-filled" size="16"
									color="#0055ff"></uni-icons>{{teamDetail.link_phone}}</view>
						</view>
						<text>房间数:{{teamDetail.room_count}}间</text>
					</view>
					<view class="title"
						style="font-size: 26rpx;font-weight: 700;color: #303133;justify-content: center;">
						<text>入住时间:{{teamDetail.enter_time_plan | moment1}}</text>
					</view>
					<view class="title"
						style="font-size: 26rpx;font-weight: 700;color: #303133;justify-content: center;">
						<text>离店时间:{{teamDetail.leave_time_plan | moment1}}</text>
					</view>
				</view>
			</view>

			<!-- 确认信息 -->
			<view class="btnSure" @click="sureInfo" v-if="!user&&teamDetail"
				:style="{background:themeColor.main_color,color:themeColor.bg_color}">
				<p v-if="!user&&teamDetail">确认团队信息</p>
			</view>

			<view class="idCardBox" v-if="user&&!ifAuth">
				<view class="title">请上传身份证正面照</view>

				<view class="picBox">
					<view class="picBox_content" style="" @click="toTakePhone">
						<view class="" v-if="idCard_img" style="height: 100%;width: 100%;">
							<image :src="idCard_img" mode="" style="height: 100%;width: 100%;border-radius: 6rpx;">
							</image>
						</view>
						<view class="" v-else>
							<text class="icon-shenfenzheng" style="font-size: 250rpx;"
								:style="{color:themeColor.com_color1+'33'}">
							</text>
							<view class="takePhoto" :style="{background:themeColor.main_color+'CC'}">
								<text class="icon-paizhao" style="font-size: 60rpx;"
									:style="{color:themeColor.bg_color}">

								</text>
							</view>
						</view>
					</view>
					<view class="msgBox">
						<p style="font-weight: 600;">请核对您的身份信息:</p>
						<view class="name">
							<p style="width: 180rpx;">姓名</p>
							<text>{{name?name:'请上传身份证识别'}}</text>
						</view>
						<view class="id_number">
							<p style="width: 180rpx;">身份证号</p>
							<p>{{id_number?id_number:'请上传身份证识别'}}</p>
						</view>
					</view>
				</view>

			</view>
			<!-- 押金 -->
			<view class="cashBox" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}"
				v-if="user">
				<view class="room" v-if="cashNum>0">
					<text style="padding-right: 80rpx;">押金( <text
							:style="totalPrice > 0?'color:#7d5a31':'color:#00aa7f'">
							{{totalPrice > 0?'待支付':'已支付'}}</text>)</text>
					<view class="" style="padding: 0;display: flex;align-items: center;">
						<text style="color: red;font-size: 28rpx;"
							:style="totalPrice> 0?'color:#aa0000':'color:#00aa7f'">￥{{cashNum}}/每间房</text>
					</view>
				</view>
				<view class="room" v-if="roomPrices.length > 0" v-for="item in roomPrices">
					<p style="padding-right: 80rpx;">房费( <text :style="totalPrice > 0?'color:#7d5a31':'color:#00aa7f'">
							{{totalPrice > 0?'待支付':'已支付'}}</text>)</p>
					<view class="" style="padding: 0;display: flex;align-items: center;">
						<text style="font-size: 28rpx;"
							:style="totalPrice > 0?'color:#aa0000':'color:#00aa7f'">￥{{item.room_price}}/每间房</text>
						<!-- <text>{{}}</text> -->
					</view>
				</view>

				<!-- <view class="room" v-if="already_pay_amount > 0">
					<p style="padding-right: 80rpx;">已支付</p>
					<view class="" style="padding: 0;display: flex;align-items: center;">
						<text style="font-size: 28rpx;color:#00aa7f">￥{{already_pay_amount}}</text>
					</view>
				</view>
				<view class="room" v-if="totalPrice > 0">
					<p style="padding-right: 80rpx;">需支付总价</p>
					<view class="" style="padding: 0;display: flex;align-items: center;">
						<text style="font-size: 28rpx;"
							:style="totalPrice > 0?'color:#aa0000':'color:#00aa7f'">￥{{totalPrice}}</text>
					</view>
				</view> -->
			</view>
			<view class="btn" v-if="name&&id_number&&idCard_img&&(user&&user.authentication==0)" @click="upThrottle"
				style="margin: 30rpx auto;height: 80rpx;width: 650rpx;border-radius: 30rpx;display: flex;align-items: center;justify-content: center;background-color: darkgreen;">
				<text style="color: #FFFFFF;">{{totalPrice > 0?'支付房费':'已支付,立即认证'}}</text>
			</view>
			<view class="btn" v-if="(!name||!id_number||!idCard_img)&&(user&&user.authentication==0)" style="margin: 30rpx auto;height: 80rpx;width: 650rpx;border-radius: 30rpx;display: flex;
				align-items: center;justify-content: center;background-color: #939393;">
				<text style="color: #FFFFFF;">请先上传身份证</text>
			</view>
			<view class="" style="height: 40rpx;">

			</view>
		</view>
		<view class="" v-if="user&&ifAuth" style="margin-top: 10rpx;background-color: #FFFFFF;position: relative;">
			<view class=""
				style="height: 100%;width: 100%;background-color: #555555;position: absolute;top: 0;bottom: 0;z-index: 90;">

			</view>
			<!-- share==1分享 2被拒绝初始化-->
			<view class="" v-if="user.share==1||user.share==2||user.share==0||user.share==-1"
				style="height: 80vh;padding: 30rpx;position: relative;z-index: 92;">

				<view class="Scanbox" style="" v-if="type==2">
					<view class="" style="height: 15vh;border-bottom: 1px dashed #303133;">
						<view class=""
							style="display: flex;justify-content: center;height: 5vh;align-items: center;font-size: 38rpx;font-weight: 700;">
							入住人:{{user.name}}
						</view>
						<view class=""
							style="height: 10vh;display: flex;align-items: center;justify-content: space-around;">
							<view class="" style="display: flex;flex-direction: column;align-items: center;">
								<view class="" style="font-size: 40rpx;font-weight: 600;">
									{{teamDetail.enter_time_plan | moment4}}
								</view>
								<view class="">
									{{teamDetail.enter_time_plan | moment}}
								</view>
							</view>
							<view class="" style="width: 80rpx;height: 1rpx;border-top:1px dashed #001032;">

							</view>
							<view class="" style="display: flex;flex-direction: column;align-items: center;">
								<view class="" style="font-size: 40rpx;font-weight: 600;">
									{{teamDetail.leave_time_plan | moment4}}
								</view>
								<view class="">
									{{teamDetail.leave_time_plan | moment}}
								</view>
							</view>
						</view>
					</view>
					<view class="" style="height: 55vh;">

						<view class=""
							style="height: 45vh;display: flex;flex-direction: column;align-items: center;margin-top: 30rpx;">
							<image v-if="user.qr_url" :src="user.qr_url" mode="" style="width: 400rpx;height: 400rpx;">
							</image>
							<view v-if="user.qr_url"
								style="font-weight: 700;font-size: 40rpx;color: #000000;display: flex;justify-content: center;margin-top: 30rpx;">
								请对方扫上面二维码-与我同住</view>
							<!-- 	<view class="custom-button" style="margin-top: 30rpx;" @click="checkInTo">
								点击立即入住(分享不会失效)
							</view> -->
						</view>
						<!-- 	<view class="" v-else>
							<view
								style="font-weight: 500;font-size: 34rpx;color: #aa0000;display: flex;justify-content: center;margin-top: 80rpx;">
								您已提前办理认证,请在入住当天,在酒店前台扫码领取钥匙!</view>
						</view> -->
					</view>
				</view>
				<!-- 方式二 -->
				<view class="Scanbox1" style="" v-if="type==1">
					<view class="" style="height: 25vh;border-bottom: 1px dashed #303133;">
						<view class=""
							style="display: flex;flex-direction: column;;justify-content: center;height: 7vh;align-items: center;font-size: 38rpx;font-weight: 700;">

							<p style="margin: 20rpx 0;font-size: 36rpx;color: #aa0000;">分享成功，等待对方确认中！！！</p>
						</view>
						<view class=""
							style="height: 13vh;display: flex;align-items: center;justify-content: space-around;">
							<view class="" style="display: flex;align-items: center;justify-content: center;"
								v-if="countNum>0">
								<view class="timeBox">{{countNum>0?Math.floor(countNum/60):0}}</view>
								<view class="timeBox">分</view>
								<view class="timeBox">{{countNum%60}}</view>
								<view class="timeBox">秒</view>
							</view>
							<view class="" v-if="countNum==0">
								<p>请分享链接给同住人</p>
							</view>
							<view class="" v-if="countNum<0">
								<p>链接已失效，请重新分享!</p>
							</view>

						</view>
						<view class="" style="display: flex;justify-content: center;">
							<p>链接失效倒计时</p>
						</view>

					</view>
					<view class="" style="height: 45vh;">
						<view class="" style="margin-top: 30rpx;display: flex;justify-content: center;">
							<view class="" @click="()=>{shereConfirm= true}"
								style="background : #ff0000;height: 80rpx;width: 440rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
								<text>查询对方是否点击链接</text>
							</view>
						</view>
						<view class=""
							style="height: 35vh;display: flex;flex-direction: column;align-items: center;margin-top: 80rpx;">
							<view class="" style=";margin-top: 30rpx;padding: 14rpx;border-radius: 8rpx;width: 100%;">
								<text style="font-size: 26rpx;font-weight: 600;">您还可以选择下面的方式办理入住</text>
								<view class=""
									style="display: flex;flex-direction: column;align-items: center;margin-top: 50rpx;">
									<view class="" @click="checkInToWait()"
										style="background : #55aa00;height: 80rpx;width: 390rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
										<text>立即入住(分享不失效)</text>
									</view>

									<view class=""
										style="display: flex;flex-direction: column;align-items: center;position: relative;margin-top: 40rpx;">
										<text style="color: #ff0000;font-size: 34rpx;">点击重新分享</text>
										<button class="shareBtn"
											style="position: absolute;bottom: 0;right: 0;width: 100%;height: 100%;z-index: 999099;opacity: 0;"
											id="shareBtn" open-type="share" type="primary">

										</button>
									</view>

								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="" v-if="user.authentication&&!user.bill_id&&user.confirm==1&&user.share_from==0"
					style="width:100%;padding: 30rpx;display: flex;flex-direction: column;align-items: center;">
					<p style="font-size: 32rpx;">正在等待主入住人{{roomName}}({{roomGender==1?'男':'女'}})再次确认，确认通过后将立即为您分配房间!</p>
				</view>

			</view>
			<view class=""
				style="position: fixed;bottom: 0;width:750rpx;height: 12vh;display: flex;z-index: 92;background-color: #FFFFFF;">
				<view class="" @click="chooseType" v-if="type==2&&team_type!=3"
					style="width: 50%;height:10vh;display: flex;flex-direction: column;justify-content: space-around;align-items: center;">

					<view class="" style="display: flex;flex-direction: column;align-items: center;">
						<text class="icon-weixin" style="font-size: 50rpx;"
							:style="type==2?'color: #4c9800;':'color: #4f4f4f'"></text>
					</view>
					<view style="width: 209rpx;
								line-height: 50rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								border-radius: 10rpx;
								font-size: 24rpx;color: #ffffff;
								padding: 10rpx;
								background: #00aa00;">邀请同住人</view>
				</view>
				<view class="" @click="scan" v-if="team_type==3"
					style="width: 50%;height:10vh;display: flex;flex-direction: column;justify-content: space-around;align-items: center;">
					<text class="icon-weixin" style="font-size: 50rpx;"
						:style="type==2?'color: #4c9800;':'color: #4f4f4f'"></text>
					<view class="" style="width: 209rpx;
								line-height: 50rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								border-radius: 10rpx;
								font-size: 24rpx;background: #4c9800;color:#FFFFFF;padding: 10rpx;">
						<view class="icon-saoma" style="font-size: 38rpx;color: #FFFFFF;"></view>
						<text style="font-size: 26rpx;font-weight: 600;">点击扫对方</text>
					</view>

				</view>
				<view class="" v-if="type==1&&team_type!=3"
					style="width: 50%;height: 10vh;display: flex;flex-direction: column;justify-content: space-around;align-items: center;border-radius: 24rpx;"
					@click="chooseType">
					<view class="icon-saoma" style="font-size: 50rpx;"
						:style="type==1?'color: #405eed;':'color: #4f4f4f'">

					</view>
					<view style="width: 209rpx;
								line-height: 50rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								border-radius: 10rpx;
								font-size: 24rpx;background: #aa0000;color:#FFFFFF;padding: 10rpx;">扫一扫面对面码</view>
				</view>
				<view class=""
					style="width: 50%;height: 10vh;display: flex;flex-direction: column;justify-content: space-around;align-items: center;border-radius: 24rpx;"
					@click="checkInTo">
					<view class="icon-duoren" style="font-size: 50rpx;color: #ffaa00;">

					</view>
					<view style="width: fit-content;
								line-height: 50rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								border-radius: 10rpx;
								font-size: 24rpx;
								background: #5d14be;
								color: #ffffff;
								padding: 10rpx;
								">立即入住自动分配</view>
				</view>
			</view>

		</view>


		<!-- <view class="" style="height: 100rpx;">

		</view> -->
		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess" @closeToLogin="toCloseLogin"></m-login>
		<!-- <m-needAuthor v-if="hackReset&&!if_login"></m-needAuthor> -->

		<!-- 识别状态弹窗 -->
		<m-popup :show="popLoading" mode="center" :closeable="false">
			<view class=""
				style="width: 400rpx;height: 300rpx;border-radius: 20rpx;display: flex;align-items: center;justify-content: center;">
				<text>正在识别...</text>
			</view>
		</m-popup>



		<!-- 支付弹窗 -->
		<m-popup :show="hackReset&&popPay" @closePop="closePop">
			<m-payCard @toPay="payFor" :payType="payList"></m-payCard>
		</m-popup>

		<!-- 提示是否有同住人 -->
		<m-popup :show="ifHaveMan" mode="center" :closeable="false">
			<view class="" style="height:360rpx;width: 600rpx;border-radius: 20rpx;">
				<view class="" style="height: 200rpx;width: 100%;padding: 40rpx;">
					<view class=""
						style="display: flex;flex-direction: column;align-items: center;justify-content: center;height: 200rpx;width: 100%;">
						<p style="font-size: 38rpx;color: #303133;font-weight: 700;">请确认您是否有指定同住人员</p>

					</view>
				</view>

				<view class=""
					style="height: 160rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="noMan()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						没有
					</view>
					<view class="" @click="haveMan()"
						style="background : #55aa7f;;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>有</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 已认证的情况下提示是否有同住人 -->
		<m-popup :show="ifHaveManAuth" mode="center" :closeable="false">
			<view class="" style="height: 360rpx;width: 600rpx;border-radius: 20rpx;">
				<view class="" style="height: 200rpx;width: 100%;padding: 40rpx;">
					<view class=""
						style="display: flex;flex-direction: column;align-items: center;justify-content: center;height: 200rpx;width: 100%;">
						<p style="font-size: 38rpx;color: #303133;font-weight: 700;">请确认您是否有同住人员</p>

					</view>
				</view>
				<view class=""
					style="height: 160rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="noManAuth()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						没有
					</view>
					<view class="" @click="haveManAuth()"
						style="background : #55aa7f;;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>有</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 提示是否同意同住人 -->
		<m-popup :show="ifArgee" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="">
						{{hotel.shop_name}}{{teamDetail.team_name}}团队的<text
							style="color: #902d24;font-weight: 700;font-size: 36rpx;">{{roomName}}{{roomGender==1?'(男)':'(女)'}}</text>邀请您同住，是否同意?
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="define()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						拒绝
					</view>
					<view class="" @click="argee()"
						:style="timeEsc==0?'background : #55aa7f;':'border: 1px solid #b6b5b5;background:#8a8a8a'"
						style="height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>同意</text>
						<text v-if="timeEsc>0">{{`(${timeEsc}s)`}}</text>
					</view>
				</view>
			</view>
		</m-popup>
		<!-- 已经认证的情况下是否同意 -->
		<m-popup :show="ifArgeeAuth" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="">
						{{hotel.shop_name}}{{teamDetail.team_name}}团队的<text
							style="color: #902d24;font-weight: 700;font-size: 36rpx;">{{roomName}}{{roomGender==1?'(男)':'(女)'}}</text>邀请您同住，是否同意?
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="defineAuth()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						拒绝
					</view>
					<view class="" @click="argeeAuth()"
						:style="timeEsc==0?'background : #55aa7f;':'border: 1px solid #b6b5b5;background:#8a8a8a'"
						style="height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>同意</text>
						<text v-if="timeEsc>0">{{`(${timeEsc}s)`}}</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 分享钥匙按钮 -->
		<m-popup mode="bottom" :show="shareShow" :closeable="false">
			<view class="shareBox">
				<p style="font-size: 26rpx;">请选择一种方式邀请同住人办理入住</p>
				<!-- <view class="icon-close" style="position: absolute;right: 10rpx;top: 10rpx;font-size: 36rpx;"
					@click="closeShare">

				</view> -->
				<view class="" style="display: flex;align-items: center;width: 100%;">
					<view class=""
						style="width: 50%;height:250rpx;display: flex;flex-direction: column;justify-content: space-around;align-items: center;">
						<view class="">
							<p style="font-weight: 700;">同住人不在身边选择</p>
						</view>
						<view class="" style="display: flex;flex-direction: column;align-items: center;">
							<text class="icon-weixin" style="font-size: 80rpx;color: #4c9800;"></text>
						</view>
						<view class=""
							style="display: flex;align-items: center;position: relative;margin-top: 40rpx;background: #50881c;height: 70rpx;width: 250rpx;justify-content: center;border-radius: 50rpx;">
							<text style="color: #ffffff;font-size: 24rpx;">分享同住人</text>
							<button class="shareBtn"
								style="position: absolute;bottom: 0;right: 0;width: 100%;height: 100%;z-index: 999099;opacity: 0;"
								id="shareBtn" open-type="share" type="primary">

							</button>
						</view>
					</view>
					<view class=""
						style="width: 50%;height: 250rpx;display: flex;flex-direction: column;justify-content: space-around;align-items: center;border-radius: 24rpx;"
						@click="chooseSc">
						<view class="">
							<p style="font-weight: 700;">同住人在一起选择</p>
						</view>
						<view class="icon-saoma" style="font-size: 90rpx;color: #405eed;">

						</view>
						<view style="width: 209rpx;
									line-height: 62rpx;
									height: 70rpx;
									background: #405eed;
									display: flex;
									align-items: center;
									justify-content: center;
									border-radius: 10rpx;
									font-size: 24rpx;color: #FFFFFF;">面对面扫一扫</view>
					</view>
				</view>


			</view>
		</m-popup>

		<!-- 对方拒绝了我的分享 -->
		<m-popup :show="elseDefine" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;">
						对方已安排房间，无法匹配，请确认是否需要立即分配房间
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="againShare()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						更换同住人
					</view>
					<view class="" @click="checkInTo()"
						style="background : #5555ff;height: 80rpx;width: fit-content;padding:10rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>立即入住自动分配</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 二次确认 -->
		<m-popup :show="againSure" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;">
						{{elseMan.name}}已确认和你同住，是否确认入住！
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="againShareTo()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						更换同住人
					</view>
					<view class="" @click="checkInAgain()"
						style="background : #5555ff;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>确认同住</text>
					</view>
				</view>
			</view>
		</m-popup>
		
		<!-- 提前二次确认 -->
		<m-popup :show="elseTimeAgin" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;">
						{{elseMan.name}}已确认和你同住，是否确认入住！
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="againShareTo()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						更换同住人
					</view>
					<view class="" @click="elseTimecheckInAgain()"
						style="background : #5555ff;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>确认同住</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 等待对方点击分享自动显示 -->
		<m-popup :show="shereConfirmAuto" mode="center" :closeable="false">
			<view class="" style="height: 560rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 400rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;margin-bottom: 20rpx;">
						<p style="font-size: 32rpx;color: #ff0000;font-weight: 700;">
							请电话通知对方尽快微信确认办理同住,若酒店房间不足系统可能随机分配人员与您同住!在此期间您可以先点击立即入住继续等待!
						</p>
					</view>

					<view class=""
						style="height: 200rpx;display: flex;align-items: center;justify-content: space-between;margin-top: 20rpx;">
						<view class="" @click="checkInToWait()"
							style="background : #55aa00;height: 80rpx;width: 340rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
							<text>立即入住(继续等待)</text>
						</view>
						<view class="" @click="againShare()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
						background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
							更换同住人
						</view>

						<!-- 		<view class="" @click="checkInTo()"
							style="background : #55aa00;height: 80rpx;width: 340rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
							<text>立即入住(分享失效)</text>
						</view> -->
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 等待对方点击分享时手动显示 -->
		<m-popup :show="shereConfirm" mode="center" :closeable="true" @closePop="closeShereConfirm">
			<view class="" style="height: 560rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 400rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<!-- 	<view class="" style="margin-top: 20rpx;font-weight: 700;display: flex;align-items: center;">
						<view style="width: 450rpx;"><text style="color: brown;">继续电话通知</text>或选择</view>
						<view class=""
							style="height: auto;width: fit-content;border-radius: 50rpx;font-size: 24rpx;
						background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;padding:12rpx 20rpx;">
							
							更换同住人
						</view>
					</view> -->
					<view class="" style="margin-top: 20rpx;margin-bottom: 20rpx;">
						<p style="font-size: 32rpx;color: #ff0000;font-weight: 700;">
							请电话通知对方尽快微信确认办理同住,若酒店房间不足系统可能随机分配人员与您同住!在此期间您可以先点击立即入住继续等待!或者也可以重新分享更换同住人!
						</p>
					</view>
					<view class=""
						style="display: flex;align-items: center;justify-content: space-between;padding:  0 0rpx;margin-top: 30rpx;">
						<view class="" @click="checkInToWait()"
							style="background : #ff5500;height: 80rpx;width: 340rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
							<text>立即入住(分享不失效)</text>
						</view>
						<view class="" @click="closeShereConfirm" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
						background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
							继续等待
						</view>
					</view>

				</view>
			</view>
		</m-popup>
		<!-- 系统已分配房间 -->
		<m-popup :show="haveRoom" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						系统已为您分配房间
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="toRoom()"
						style="background : #55aa7f;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>去房卡页</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 办理时间超时请联系酒店 -->
		<m-popup :show="timeOutShow" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						办理时间超时请联系酒店
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="exit()"
						style="background : #aa0000;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>退出</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 联系同住人 -->
		<m-popup :show="linkShow" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						请联系同住人办理入住或者您可以选择先入住,继续等待对方确认!
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="checkInToWait()"
						style="background : #55aa7f;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>确认</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 等待二次确认 -->
		<m-popup :show="awaitArgee" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 200rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;color: #aa0000;">等待对方确认中</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						等待分享人确认后，系统将为您分配房间!您可以电话联系分享人员进行二次确认! 
					</view>
				</view>
				<view class=""
					style="height: 200rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<text style="font-size: 34rpx;font-weight: 600;">等待对方确认中...</text>
					<!-- <view class="" @click="toSure()"
						style="background : #55aa7f;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>确认</text>
					</view> -->
				</view>
			</view>
		</m-popup>

		<!-- 已有房间等待分配 -->
		<m-popup :show="checkInShare" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;">
						您已分配房间，是否同意与{{roomName}}{{roomGender==1?'(男)':'(女)'}}同住
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="toDefine()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						不同意
					</view>
					<view class="" @click="toAgree()"
						style="background : #5555ff;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>同意</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 没有订单 -->
		<m-popup :show="noDetail" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						链接已失效，<text style="font-weight: 700;color: brown;">请前往酒店前台扫码办理!</text>
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="back()"
						style="background : #5555ff;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>确认</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 时间过早或过晚 -->
		<m-popup :show="earthTime" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;text-align: center;">
						您正在提前办理认证,请在入住当天,在酒店前台扫码领取钥匙!
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="back()"
						style="background : #5555ff;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>确认</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 未认证 -->
		<m-popup :show="toAuth" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						您已分配房间，{{roomName}}{{roomGender==1?'(男)':'(女)'}}同住，请去认证后获得钥匙!
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="to_auth()"
						style="background : #5555ff;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>确认</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 不能接受分享 -->
		<!-- 认证 -->
		<m-popup :show="noShareAuth" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">温馨提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						会务组已单独为您安排房间,请继续办理入住!
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class=""
						style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
						<view class="" @click="againShare()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
						background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
							更换同住人
						</view>
						<view class="" @click="checkInTo()"
							style="background : #5555ff;height: 80rpx;width: fit-content;padding:10rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
							<text>立即入住自动分配</text>
						</view>
					</view>
				</view>
			</view>
		</m-popup>
		<!-- 不能接受分享 -->
		<!-- 未认证 -->
		<m-popup :show="noShare" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">温馨提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						会务组已单独为您安排房间,请前往酒店扫描团队码入住!
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="back()"
						style="background : #5555ff;height: 80rpx;width: fit-content;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;padding: 0 20rpx;">
						<text>请到达酒店现场扫团队码办理！</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 分享人拒绝 -->
		<m-popup :show="elseNoShare" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;">
						会务组已为对方安排房间，无法匹配！
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="back()" v-if="user.authentication==0" style="height: 80rpx;width: fit-content;border-radius: 50rpx;padding: 0 40rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						请到达酒店现场扫团队码办理！
					</view>
					<view class="" @click="checkInTo()" v-if="user.authentication==1"
						style="background : #5555ff;height: 80rpx;width: fit-content;padding:10rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>立即入住自动分配</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 被分享人已经安排单间 -->
		<m-popup :show="elseNoShareHave" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;">
						会务组已为对方安排房间，无法匹配！
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">
					<view class="" @click="againShareHave()" style="height: 80rpx;width: 240rpx;border-radius: 50rpx;
					background: #902d24;display: flex;align-items: center;justify-content: center;color: #FFFFFF;">
						更换同住人
					</view>
					<view class="" @click="checkInTo()" v-if="user.authentication==1"
						style="background : #5555ff;height: 80rpx;width: fit-content;padding:10rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>立即入住自动分配</text>
					</view>
				</view>
			</view>
		</m-popup>
		<!-- 办理入住已分享 -->
		<m-popup :show="checkInMain" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">温馨提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						您已经认证成功,可以直接选择入住.如您已经分享同住人,您的分享会保留,请尽快联系同住人办理入住
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="checkInAwait()"
						style="background : #5555ff;height: 80rpx;width: fit-content;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;padding: 0 20rpx;">
						<text>立即入住(分享保留)</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 办理入住无分享 -->
		<m-popup :show="waitShow" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">温馨提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;display: flex;justify-content: center;">
						您已经认证成功,可以直接选择入住.
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="checkInAwait()"
						style="background : #5555ff;height: 80rpx;width: fit-content;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;padding: 0 20rpx;">
						<text>立即入住</text>
					</view>
				</view>
			</view>
		</m-popup>


		<m-popup mode="center" :show="showDevice">
			<view class="deviceBox">
				
				<view class="icon-wuxinhao" style="font-size: 240rpx;color: brown;margin-top: 30rpx;">
				</view>
				<view class="" style="padding: 30rpx;">
					<p>请到酒店大堂扫码领取电子钥匙</p>
					<p style="font-size: 22rpx;margin-top: 20rpx;">酒店大厅蓝牙U盾正在检测中...</p>
				</view>
				<view class="deBtn1" @click="reload()">
					重新检测
				</view>
			</view>
		</m-popup>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	import mStepLine from '../components/m-stepLine/m-stepLine.vue';
	export default {
		data() {
			return {
				teamDetail: "",
				teamRoomList: null,
				if_login: false,
				hackReset: true,
				idCard_img: '',
				id_number: '',
				name: '',
				popLoading: false,
				roomInfo: null, //排房信息
				ifBao: false, //判断是否可以包房
				roomList: [],
				// chooseId:'',
				// sureBao:false,//是否包房
				popPay: false, //支付弹窗
				payList: ['weixin', 'tongyong', 'duli'], //支付方式
				blueDeviceSetting: false,
				cashNum: 0, //押金
				roomPrices: [], //房费
				totalPrice: 0, //总房价
				already_pay_amount: 0, //已经支付
				billId: '',
				code: '', //分享过来的code,确认团队使用，
				if_tong: '', //是否同意确认同住人,
				roomName: '',
				roomGender: '',
				ifArgee: false,
				ifArgeeAuth: false,
				timeEsc: 3,
				timeEscShare: 0,
				ifHaveMan: false, //是否选择同住人
				ifHaveManAuth: false, //认证的情况下提示是否有同住人
				team_id: '',
				manNum: 1,
				ifAuth: false, //是否认证
				share_code: '',
				shareShow: false,
				user: null,
				// countdownShow: false,
				countNum: 0,

				// current: 0,
				stepList: ['确认团队', '上传信息', '支付房费', '身份验证', '完成'],

				shareHandle: false, //分享钩子
				elseMan: null,
				timeAsc: null,
				params: {
					team_id: null,
					share_code: null
				},
				elseDefine: false, //对方拒绝分享邀请
				againSure: false, //对方同意分享二次确认
				haveRoom: false, //已分配房间弹窗
				timeOutShow: false, //超时弹窗
				linkShow: false, //联系同住人
				awaitArgee: false, //等待二次确认
				checkInShare: false, //已有房间，拉人入房
				timeInfo: null, //查询订单定时器
				img_src: '', //扫码图片
				noDetail: false, //链接失效
				type: 2, //模式1，是分享 模式2是扫码
				agreeFlag: 0, //是否同意的辨识改变后只出现一次
				haveFlag: 0, //是否有同住人只出现一次
				defindFlag: 0, //对方拒绝你的弹窗只谈一次
				defindFlagHave: 0,
				toAuth: false, //付了款未认证的情况
				userList: [], //当前的链接人数
				shereConfirm: false, //分享等待的弹窗
				shereConfirmAuto: false, //自动弹窗
				ifshereConfirmAuto: true,
				confirmNum: 0,
				confirmTime: null,
				team_type: 0, //1,无名单 2，有名单
				noShare: false, //不能接受分享弹窗
				noShareAuth: false, //已经认证的情况无权接受分享
				elseNoShare: false, //对方没有权限提示
				share_usable: '', //是否支持分享
				canClick: 5,
				elseNoShareHave: false,
				room_id: '',
				earthTime: false, //未到入住时间提示
				ifEarth: false,
				checkInMain: false,
				waitShow: false,
				single: '',
				showDevice: false, //是否检查蓝牙
				blueList: [],
				elseTimeAgin:false

			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting', 'setting']),
			current() {
				if (this.user && !this.idCard_img && this.totalPrice > 0 && !this.ifAuth) {
					return 0
				} else if (this.user && this.idCard_img && this.totalPrice > 0 && !this.ifAuth) {
					return 1
				} else if (this.user && this.idCard_img && this.totalPrice == 0 && !this.ifAuth) {
					return 2
				} else if (this.user && this.idCard_img && this.totalPrice == 0 && this.ifAuth) {
					return 3
				} else if (this.ifAuth && this.totalPrice == 0) {
					return 4
				}
			}
		},
		components: {
			mStepLine
		},
		watch: {
			haveRoom: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.awaitArgee = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.awaitArgee = false
						this.elseNoShare = false
						this.elseNoShareHave = false
					}
				},
				immediate: true,
				deep: true
			},
			elseNoShareHave: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.awaitArgee = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.awaitArgee = false
						this.elseNoShare = false
						this.haveRoom = false
					}
				},
				immediate: true,
				deep: true
			},
			elseTimeAgin:{
				handler(newVal, oldVal) {
					if (newVal) {
						this.haveRoom = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.awaitArgee = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.elseNoShareHave = false
					}
				},
				immediate: true,
				deep: true
			},
			elseDefine: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.haveRoom = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.awaitArgee = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.elseNoShareHave = false
					}
				},
				immediate: true,
				deep: true
			},
			againSure: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.haveRoom = false //对方拒绝分享邀请
						this.elseDefine = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.awaitArgee = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.elseNoShareHave = false
					}
				},
				immediate: true,
				deep: true
			},
			timeOutShow: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.timeInfo && clearInterval(this.timeInfo)
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.haveRoom = false //超时弹窗
						this.linkShow = false //联系同住人
						this.awaitArgee = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.elseNoShareHave = false
					}
				},
				immediate: true,
				deep: true
			},
			linkShow: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.haveRoom = false //联系同住人
						this.awaitArgee = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.elseNoShareHave = false
					}
				},
				immediate: true,
				deep: true
			},
			awaitArgee: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.haveRoom = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.elseNoShareHave = false
						this.checkInMain = false //分享时提前入住
					}
				},
				immediate: true,
				deep: true
			},
			waitShow: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.haveRoom = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.elseNoShareHave = false
						this.checkInMain = false //分享时提前入住
					}
				},
				immediate: true,
				deep: true
			},
			checkInMain: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.haveRoom = false //等待二次确认
						this.checkInShare = false //已有房间，拉人入房
						this.noDetail = false
						this.elseNoShareHave = false
						this.waitShow = false //分享时提前入住
					}
				},
				immediate: true,
				deep: true
			},
			checkInShare: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.awaitArgee = false
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.haveRoom = false //已有房间，拉人入房
						this.noDetail = false
						this.elseNoShareHave = false
						this.checkInMain = false //分享时提前入住
					}
				},
				immediate: true,
				deep: true
			},
			noDetail: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.timeInfo && clearInterval(this.timeInfo)
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.awaitArgee = false //等待二次确认
						this.haveRoom = false //已有房间，拉人入房
						this.checkInShare = false
						this.elseNoShareHave = false
						this.checkInMain = false //分享时提前入住
					}
				},
				immediate: true,
				deep: true
			},
			elseNoShare: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.elseDefine = false //对方拒绝分享邀请
						this.againSure = false //对方同意分享二次确认
						this.timeOutShow = false //超时弹窗
						this.linkShow = false //联系同住人
						this.awaitArgee = false //等待二次确认
						this.haveRoom = false //已有房间，拉人入房
						this.checkInShare = false
						this.elseNoShareHave = false
						this.checkInMain = false //分享时提前入住
					}
				},
				immediate: true,
				deep: true
			},
			noShare: {
				handler(newVal, oldVal) {
					if (newVal) {
						if (this.ifArgeeAuth) {
							this.ifArgeeAuth = false
						}
					}
				},
				immediate: true,
				deep: true
			},
			idCard_img: {
				handler(newVal, oldVal) {
					if (newVal) {
						console.log('bb1');
						this.$iBox
							.http('getTeamRoomBillInfo', this.params)({
								method: 'post'
							})
							.then(res => {
								if (!res.data) {
									this.noDetail = true
									return
								}
								this.teamDetail = res.data
								this.userList = res.data.users
								// this.type = res.data.show_type
								// 记录是否是有名单团队或无名单团队，team_type，1无名单，2有名单，3,旅行团 show_type1展示二维码，2展示链接
								this.team_type = res.data.team_type

								let auth = res.data.users.filter(item => {
									return item.common_code == this.userInfo.common_code
								})[0]
								if (auth) {
									this.user = auth
									this.ifAuth = auth.authentication
									// 无名单时弹出正常,无名单中的有名单或者有名单上传身份证弹出
									if (this.userList.length == 1) {

										if (this.user && this.user.share == 2 && this.share_usable == 1 && this
											.team_type != 3) {

											this.ifHaveManAuth = true
										}
									} else {
										this.elseMan = res.data.users.filter(item => {
											return item.common_code != this.userInfo.common_code
										})[0]

										console.log('this.user', this.share_code);
										if (auth.share_from == 0 && auth.accept == 0) { //不能接受分享
											this.share_code = this.elseMan.share_code
											let params = {
												team_id: this.teamDetail.id,
												share_code: this.elseMan.share_code,
												confirm: 0
											}

											this.$iBox.http('chooseStayUser', params)({
													method: 'post'
												})
												.then(res => {
													this.$iBox.http('getQrInfo', {
															code: this.elseMan.share_code
														})({
															method: 'post'
														})
														.then(res => {
															if (res.data) {
																//status0,未点击|1，点击过|2,过期|3，拒绝|4.无权接受
																if (res.data.status == 4 && auth
																	.authentication == 0) {
																	this.noShare = true
																} else if (res.data.status == 4 && auth
																	.authentication == 1 && (!auth.bill_id || (
																		auth.bill_id && (auth
																			.user_stay_status == 0 || !auth
																			.user_stay_status)))) {
																	if (this.shareShow) {
																		this.noShareAuth = false
																	} else {
																		this.noShareAuth = true
																	}

																}
															}
														})
												})

										} else if (auth.share_from == 0 && auth.accept == 1) {
											this.share_code = this.elseMan.share_code
											if (auth.team_confirm == 0) {
												uni.reLaunch({
													url: '/packageB/teamCheckIn/waitTeam?team_id=' + this
														.teamDetail.id + '&share_code=' + this.share_code
												})
											} else {
												if (auth.confirm == 0 && this.team_type == 2 && (!auth.bill_id || (auth
														.bill_id && (auth.user_stay_status == 0 || !auth
															.user_stay_status)))) {
													this.ifArgeeAuth = true
													let a = setInterval(item => {
														if (this.timeEsc <= 0) {
															clearInterval(a)
														} else {
															this.timeEsc--
														}
													}, 1000)
												}
											}

										}
									}
									// 有名单时分享，有名单团队

								}
							})
						// 上传身份证首先判断团队是否是有名单,有名单且只有一人且share==2则弹是否有同住人

					}
				},
				immediate: true,
				deep: true
			},
		},
		async onLoad(options) {
			await this.$onLaunched;
			// uni.showLoading({
			// 	title: '加载中...'
			// })
			console.log('---', options);
			
			this.$nextTick(() => {
				this.hackReset = true
			})
			this.cusStyle = {
				zindex: 1001
			}

			// 查询身份证信息
			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					this.action_idCard = res.data + '/wx/User/userOcr'
				}
			});

			//是否是会员
			let set = this.setting.filter(item => {
				return item.sign == 'auto_register_member'
			})
			if (set[0].property) {
				let a = set[0].property.value
				if (a == 2) {
					if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
						.upgrade_growth_value > -
						1) {
						this.if_login = false
						let self = this
						this.timeInfo && clearInterval(this.timeInfo)
						this.timeInfo = setInterval((function target() {
							self.scanExc()
							// self.team_id = options.t
							// self.params.team_id = options.t
							// self.getTeamInfo()
							return target
						})(), 5000)
					} else {
						this.if_login = true
					}
				} else if (a == 1) {
					// this.pop = true
					if (this.userInfo.phone) {
						this.if_login = false
						let self = this
						this.timeInfo && clearInterval(this.timeInfo)
						this.timeInfo = setInterval((function target() {
							self.scanExc()
							return target
						})(), 5000)
					} else {
						this.if_login = true
					}
				}
			}
		},
		// share = 1代表
		async onShow(options) {
			await this.$onLaunched;
			console.log(options,'show');
			// 查询蓝牙设置
			this.blueDeviceSetting = this.shopSetting.filter(item => {
				return item.sign == 'u_dun_device'
			})[0].property.status

			this.$iBox.http('getBlueTooth', {
				shop_id: this.hotel.id
			})({
				method: 'post'
			}).then(res => {

				this.blueList = res.data.filter(item => item.bluetooth_type === 1);
				console.log(this.blueList, 'this.blueList');

			})

		},
		methods: {
			...mapActions('room', ['getBillDetail']),
			async blueGet() {
				try {
					// 开始搜索设备（等待搜索结果）
					return await this.peiDui();


				} catch (error) {
					console.error('获取蓝牙配置失败:', error);
					this.showDevice = true; // 显示异常状态
					return false;
				}
			},
			scanExc() {
				let scene = wx.getEnterOptionsSync()
				this.params = {
					team_id: null,
					share_code: null
				}
				console.log(scene, 'scene',scene.query,scene.query.scene);
				// 扫码进来判断scene.query.scene和场景值1047,如果是扫码进来取得到值就要存一下缓存
				if (scene.query && Object.keys(scene.query).length !== 0 && scene.query.scene && (scene.scene==1047||scene.scene==1048||scene.scene==1049)) {
					uni.setStorageSync(
						'sence', { value: scene.query.scene, ts: Date.now() }
					)
					let query = decodeURIComponent(scene.query.scene)
					console.log(query,'jjk');
					//解析参数
					if (query.includes("t=") && !query.includes("tc=")&&!query.includes("sg=")) {
						console.log(query, 'scene12');
						this.team_id = this.$iBox.linkFormat(query, "t")
						this.params.team_id = this.$iBox.linkFormat(query, "t")
					} else if (query.includes("tc=")) {
						console.log(query, 'query2');
						this.share_code = this.$iBox.linkFormat(query, "tc")
						this.params.share_code = this.$iBox.linkFormat(query, "tc")
						this.team_id = this.$iBox.linkFormat(query, "t")
						this.params.team_id = this.$iBox.linkFormat(query, "t")
						this.$iBox.http('getQrInfo', {
								code: this.share_code
							})({
								method: 'post'
							})
							.then(res => {
								if (res.data) {
									this.roomName = res.data.from_user_name
									this.roomGender = res.data.from_user_gender == "男" ? 1 : 2
								}
					
							})
					}
					
					if (query.includes("bi=")) {
						this.room_id = this.$iBox.linkFormat(query, "bi")
					}
					console.log(query.includes("t=") &&query.includes("sg="),'hhn');
					 if (query.includes("t=") &&query.includes("sg=")) {
						 console.log();
						this.team_id = this.$iBox.linkFormat(query, "t")
						this.single = this.$iBox.linkFormat(query, "sg")
						this.params.team_id = this.$iBox.linkFormat(query, "t")
					}
					
				}else if(scene.query&&scene.query.teamId&&scene.query.code){
					// scene.query.teamId和有值就代表是通过分享卡片进入
					this.team_id = scene.query.teamId
					this.share_code = scene.query.code
					this.$iBox.http('getQrInfo', {
							code: this.share_code
						})({
							method: 'post'
						})
						.then(res => {
							if (res.data) {
								this.roomName = res.data.from_user_name
								this.roomGender = res.data.from_user_gender == "男" ? 1 : 2
							}
						})
					
					this.params.team_id = this.team_id
					this.params.share_code = this.share_code
				}else if(scene.query&&scene.query.teamId&&!scene.query.code) {
					this.team_id = scene.query.teamId
					this.params.team_id = this.team_id
				}else if(Object.keys(scene.query).length == 0) {
					const _cached = uni.getStorageSync('sence')
					let _cachedScene = ''
					if (_cached) {
						if (typeof _cached === 'object' && _cached.value && _cached.ts) {
							if (Date.now() - _cached.ts <= 60 * 1000) {
								_cachedScene = _cached.value
							}
						} else if (typeof _cached === 'string') {
							_cachedScene = _cached
						}
						uni.removeStorageSync('sence')
					}
					scene.query.scene = _cachedScene
					let query = decodeURIComponent(scene.query.scene || '')
					//解析参数
					if (query.includes("t=") && !query.includes("tc=")) {
						console.log(query, 'scene12');
						this.team_id = this.$iBox.linkFormat(query, "t")
						this.params.team_id = this.$iBox.linkFormat(query, "t")
					} else if (query.includes("tc=")) {
						console.log(query, 'query2');
						this.share_code = this.$iBox.linkFormat(query, "tc")
						this.params.share_code = this.$iBox.linkFormat(query, "tc")
						this.team_id = this.$iBox.linkFormat(query, "t")
						this.params.team_id = this.$iBox.linkFormat(query, "t")
						this.$iBox.http('getQrInfo', {
								code: this.share_code
							})({
								method: 'post'
							})
							.then(res => {
								if (res.data) {
									this.roomName = res.data.from_user_name
									this.roomGender = res.data.from_user_gender == "男" ? 1 : 2
								}
					
							})
					}
					
					if (query.includes("bi=")) {
						this.room_id = this.$iBox.linkFormat(query, "bi")
					}
					
					 if (query.includes("t=") &&query.includes("sg=")) {
						this.team_id = this.$iBox.linkFormat(scene.query, "t")
						this.single = this.$iBox.linkFormat(scene.query, "sg")
						this.params.team_id = this.$iBox.linkFormat(query, "t")
					}
					
				} else {
					console.log(this.team_id, 'this.team_id1');
					if (this.team_id) {
						this.params.team_id = this.team_id
					} else {
						uni.hideLoading()
						uni.showModal({
							title: '团队码错误',
							content: '请重新扫码',
							showCancel: false,
							success: res => {
								if (res.confirm) {
									//直接退出小程序
									uni.exitMiniProgram()
								}
							}
						})
					}

				}

				this.getTeamInfo()
			},
			timeOut() {
				let time = this.shopSetting.filter(item => {
					return item.sign == 'enter_time'
				})[0].property.value

				let start = this.$moment(this.teamDetail.enter_time_plan * 1000).format('YYYY-MM-DD') + ' ' + time + ':00'
				start = this.$moment(start, 'YYYY-MM-DD hh:mm:ss').unix()

				let now = this.$moment().unix()
				console.log(now, start, this.teamDetail.leave_time_plan, 'kl');
				if (now < start || now > this.teamDetail.leave_time_plan) {
					console.log(now, start, this.teamDetail.leave_time_plan, 'kl1');
					this.earthTime = true
					return;

				}
			},
			getTeamInfo() {
				// uni.showLoading({
				// 	title: '加载中...'
				// })
				//查询订单信息
				this.$iBox
					.http('getTeamRoomBillInfo', this.params)({
						method: 'post'
					})
					.then(res => {
						if (!res.data) {
							this.noDetail = true
							return
						}

						this.teamDetail = res.data
						this.userList = res.data.users

						// 未到入住时间直接拦截
						let time = this.shopSetting.filter(item => {
							return item.sign == 'enter_time'
						})[0].property.value

						let start = this.$moment(this.teamDetail.enter_time_plan * 1000).format('YYYY-MM-DD') + ' ' +
							time + ':00'
						start = this.$moment(start, 'YYYY-MM-DD hh:mm:ss').unix()

						let now = this.$moment().unix()

						// 记录是否是有名单团队或无名单团队，team_type，1无名单，2有名单，3，旅行团show_type1展示二维码，2展示链接
						this.team_type = res.data.team_type
						this.share_usable = res.data.share_usable

						uni.hideLoading()
						// 首先判断是否超时
						if (res.data.leave_time_plan < this.$moment().unix()) {
							this.timeOutShow = true
						} else {
							// 再判断是否交了房费
							this.$iBox.http('getPayAmount', {
									team_id: this.teamDetail.id
								})({
									method: 'post'
								})
								.then(res => {
									this.cashNum = res.data.cash_pledge
									this.roomPrices = res.data.room_price
									this.totalPrice = res.data.total_amount
									this.already_pay_amount = res.data.already_pay_amount
								})

							// 1.判断如果是users=0的情况必然是扫码进入 share_usable，0不允许分享，1运行分享
							if (res.data.users.length == 0 && !this.haveFlag && this.team_type == 1 && this
								.share_usable == 1) {

								this.ifHaveMan = true

							} else if (res.data.users.length > 0) {
								let auth = res.data.users.filter(item => {
									return item.common_code == this.userInfo.common_code
								})[0]
								console.log(auth, 'wodeauth');
								if (auth) {
									this.user = auth
									if (auth.identification_image) {
										this.idCard_img = auth.identification_image
										this.id_number = auth.identification_number
										this.name = auth.name
									}
									this.ifAuth = auth.authentication
									this.countNum = Math.floor(auth.share_limit - this.$moment().unix())

								}
								// 2.当users列表等于1且是自己时的时候，则必然是可以分享的，可能为扫码进入，或者是点链接后拒绝了与对方同住
								if (res.data.users.length == 1) {
									//user有一个但是没有自己则代表点击他人的链接进入，需要弹出同意入住
									if (!auth && !this.agreeFlag && this.team_type != 2) {

										this.ifArgee = true
										let a = setInterval(item => {
											if (this.timeEsc <= 0) {
												clearInterval(a)
											} else {
												this.timeEsc--
											}
										}, 1000)

									} else if (auth) {
										//user只有一个且是自己，需要通过share_from判断 1.等于1是扫团队码进入 2.等于0是点别人链接进入且确认团队了也会只有自己，
										// team_confirm==0代表选择单人已经进入了排房流程，-1默认,分享被点击会变成1,如果等于1则代表进入了排房流程，如果不等于0则代表已经排房billID且user_stay_status==1则代表已经入住,如果没有billid则代表进入

										if (auth.team_confirm == 0) {
											this.timeOut()
											uni.reLaunch({
												url: '/packageB/teamCheckIn/waitTeam?team_id=' + this
													.teamDetail.id
											})
										} else {

											if (auth.share_from == 1) {
												//share==0代表还没有分享或者没有选择同住人,判断Billid及入住状态user_stay_status==1则代表已经排房，不管auth.team_confirm==2或是-1，只要有房间则弹出您已排房弹框
												if (auth.share == 0) {
													if (auth.bill_id && (auth.user_stay_status == 1)) {
														this.haveRoom = true
													} else if (auth.authentication == 1 && auth.team_confirm == -1) {
														let time = this.shopSetting.filter(item => {
															return item.sign == 'enter_time'
														})[0].property.value

														let start = this.$moment(this.teamDetail.enter_time_plan *
															1000).format('YYYY-MM-DD') + ' ' + time + ':00'
														start = this.$moment(start, 'YYYY-MM-DD hh:mm:ss').unix()

														let now = this.$moment().unix()
														console.log(now, start, this.teamDetail.leave_time_plan, 'kl');
														if (now < start || now > this.teamDetail.leave_time_plan) {
															console.log(now, start, this.teamDetail.leave_time_plan,
																'kl1');
															this.earthTime = true
															return;

														} else {
															this.waitShow = true
															// uni.showModal({
															// 	title: '提示',
															// 	content: '您已通过认证,点击立即入住获取房卡!',
															// 	confirmText: '立即入住',
															// 	showCancel: false,
															// 	success: res => {
															// 		this.$iBox.http('teamCheckIn', {
															// 			teamId: this.team_id
															// 		})({
															// 			method: 'post'
															// 		}).then(res => {
															// 			uni.reLaunch({
															// 				url: '/pages/myRoom/myRoom'
															// 			})
															// 		})
															// 	}
															// })
														}

													}
												} else if (auth.share == 1) {
													//share==1选择有同住人的时候会变成1,跟确认团队传的confirm=1相关

													if (auth.authentication == 1 && !auth.share_code) {
														// 1.这个时候我认证了，但是没有分享码，就是我选择了有同住人但是没有分享的情况下需要提醒一下去分享
														//没有showcode如果是未认证show_type1展示二维码，2展示链接
														if (this.teamDetail.show_type !== 2 && this.team_type != 3) {
															this.shareShow = true
														} else {
															console.log('meiyou');
															this.shareShow = false
															this.type = this.teamDetail.show_type
															this.$iBox.http('getSameRoomCode', {
																team_id: this.team_id
															})({
																method: 'post'
															}).then(res => {
																this.share_code = res.data.code
																// this.team_id = res.data.team_id
																//查询订单信息

																this.$iBox
																	.http('getTeamRoomBillInfo', this.params)({
																		method: 'post'
																	})
																	.then(res => {
																		uni.hideLoading()
																		this.teamDetail = res.data
																		let auth = res.data.users.filter(
																			item => {
																				return item
																					.common_code ==
																					this
																					.userInfo
																					.common_code
																			})[0]
																		if (auth) {
																			this.user = auth
																			this.ifAuth = auth
																				.authentication
																		}

																		// 获取另外一人的信息
																		this.elseMan = res.data.users
																			.filter(
																				item => {
																					return item
																						.common_code !=
																						this
																						.userInfo
																						.common_code
																				})[0]
																	})

															})
														}


													} else if (auth.authentication == 1 && auth.share_code) {
														this.shareShow = false
														// 有share_from有两种。1一种是分享给别人,这时候需要查询码，提醒去联系同住人
														this.$iBox.http('getQrInfo', {
																code: auth.share_code
															})({
																method: 'post'
															})
															.then(res => {
																if (res.data) {
																	//status0,未点击|1，点击过|2,过期|3被分享人拒绝，4分享人拒绝
																	// 自动只弹一次，隔10秒弹便关闭，提醒去联系同住人
																	if (res.data.status == 4 && !this
																		.defindFlagHave) {
																		this.elseNoShareHave = true
																	} else if (res.data.status == 1) {
																		this.confirmTime && clearTimeout(this
																			.confirmTime)
																		this.confirmTime = setTimeout(() => {
																			if (this.ifshereConfirmAuto &&
																				res
																				.data
																				.show_type == 2) {
																				this.shereConfirmAuto =
																					true
																				this.ifshereConfirmAuto =
																					false
																			}

																		}, 1000)
																	}

																}
															})
													}
												} else if (auth.share == -1 && !this.defindFlag) {
													// share=-1且有share_code代表对方拒绝了我的邀请重新请求code会重新变成share=1
													// clearInterval(this.timeInfo)
													this.elseDefine = true
													// share=2且是旅行团认证之后面对面扫码被拒绝的状态
												} else if (auth.share == 2 && auth.authentication == 1 && !auth
													.share_code) {
													if (this.teamDetail.show_type == 2 && this.team_type == 3) {
														this.shareShow = false
														this.type = this.teamDetail.show_type
														this.$iBox.http('getSameRoomCode', {
															team_id: this.team_id
														})({
															method: 'post'
														}).then(res => {
															this.share_code = res.data.code
															// this.team_id = res.data.team_id
															//查询订单信息

															this.$iBox
																.http('getTeamRoomBillInfo', this.params)({
																	method: 'post'
																})
																.then(res => {
																	uni.hideLoading()
																	this.teamDetail = res.data
																	let auth = res.data.users.filter(
																		item => {
																			return item
																				.common_code ==
																				this
																				.userInfo
																				.common_code
																		})[0]
																	if (auth) {
																		this.user = auth
																		this.ifAuth = auth
																			.authentication
																	}

																	// 获取另外一人的信息
																	this.elseMan = res.data.users
																		.filter(
																			item => {
																				return item
																					.common_code !=
																					this
																					.userInfo
																					.common_code
																			})[0]
																})

														})
													}
												}
											} else {
												// share_from=0,被分享人
												if (auth.share != 2) {
													this.$iBox.http('getQrInfo', {
															code: this.share_code
														})({
															method: 'post'
														})
														.then(res => {
															if (res.data) {
																console.log(auth.share_code, 'auth.share_code');
																//status0,未点击|1，点击过|2,过期|3被分享人拒绝，4分享人拒绝，被分享人拒绝，需要给提示，并关闭小程序提示现场扫码
																if (res.data.status == 4) {
																	this.elseNoShare = true
																} else if (res.data.status == 2) {
																	this.noDetail = true
																}
															}
														})
												} else if (auth.share == 2 && !this.haveFlag && this.share_usable ==
													1) {
													if (this.awaitArgee) {
														this.awaitArgee = false
													}
													this.$iBox.http('getQrInfo', {
															code: this.share_code
														})({
															method: 'post'
														})
														.then(res => {
															if (res.data) {
																console.log(auth.share_code, 'auth.share_code');
																//status0,未点击|1，点击过|2,过期|3被分享人拒绝，4分享人拒绝，被分享人拒绝，需要给提示，并关闭小程序提示现场扫码
																if (res.data.status == 4) {
																	this.elseNoShare = true
																} else if (res.data.status == 2) {
																	this.noDetail = true
																}
															}
														})

												}


											}
										}


									}
								} else if (res.data.users.length == 2) {
									// 有两个人且我是主入住人的情况下,我必定是已经通过认证,因为只有认证后才能分享
									// 获取另外一人的信息
									this.elseMan = res.data.users.filter(item => {
										return item.common_code != this.userInfo.common_code
									})[0]
									if (auth.share_from == 1) {
										this.share_code = auth.share_code
										// auth.share_from等于1代表的是分享人,分享人在2人入住的时候需要3种提示：1，已分配房间,2,二次确认 3.联系同住人办理
										this.$iBox.http('getQrInfo', {
												code: this.share_code
											})({
												method: 'post'
											})
											.then(res => {

												if (res.data) {
													this.roomName = res.data.from_user_name
													this.roomGender = res.data.from_user_gender == "男" ? 1 : 2
													// 当有房间的情况下,因为我是分享的人则代表我肯定已经入住了，直接提示

													// if (auth.team_confirm == 0) {
													// 	this.timeOut()
													// 	uni.reLaunch({
													// 		url: '/packageB/teamCheckIn/waitTeam?team_id=' +
													// 			this
													// 			.teamDetail.id + '&share_code=' + this
													// 			.share_code
													// 	})
													// }
													// auth.team_confirm == -1则代表我没有二次确认我可以先入住
													if (auth.authentication == 1 && auth.team_confirm == -1&&auth.confirm==0) {
														let time = this.shopSetting.filter(item => {
															return item.sign == 'enter_time'
														})[0].property.value

														let start = this.$moment(this.teamDetail
																.enter_time_plan * 1000).format('YYYY-MM-DD') +
															' ' + time + ':00'
														start = this.$moment(start, 'YYYY-MM-DD hh:mm:ss')
															.unix()

														let now = this.$moment().unix()
														console.log(now, start, this.teamDetail
															.leave_time_plan, 'kl');
														if (now < start || now > this.teamDetail
															.leave_time_plan) {
															
															// 在分享人没有房间的情况下，有两种情况，对方未确认认证和我没有二次确认
															// 对方处于付款前的状态，需要提醒对方付款
															if (auth.share == 1 && this.elseMan.confirm == 0 &&
																auth.share_code) {
																this.linkShow = true
																// 对方已经付款，分享人可以进行二次确认进行入住操作
															} else if (auth.share == 1 && this.elseMan.confirm ==
																1) {
																// confirmSame二次确认入住,提前二次确认不能入住
																this.elseTimeAgin = true
																
															} else if (auth.share == 1 && this.elseMan.confirm ==
																2 && auth.confirm==0) {
																// confirmSame已经二次确认入住
																this.againSure = false
																this.earthTime = true
																return;
															}
															
															

														} else if (auth.bill_id && (auth.user_stay_status == 1)) {

															this.haveRoom = true

														} else {
															console.log(auth.authentication, auth.share, this
																.elseMan, 'll;');
															// 在分享人没有房间的情况下，有两种情况，对方未确认认证和我没有二次确认
															// 对方处于付款前的状态，需要提醒对方付款
															if (auth.share == 1 && this.elseMan.confirm == 0 &&
																auth.share_code) {
																this.linkShow = true
																// 对方已经付款，分享人可以进行二次确认进行入住操作
															} else if (auth.share == 1 && this.elseMan.confirm ==
																1) {
																// confirmSame二次确认入住
																console.log(auth.authentication, auth.share, this
																	.elseMan, 'll;');
																this.againSure = true
															} else if (auth.share == 1 && this.elseMan.confirm ==
																2 && auth.confirm==0) {
																// confirmSame已经二次确认入住
																this.againSure = false
																this.waitShow = true
															}
														}
													}

												}
											})
									} else if (auth.share_from == 0) {
										this.share_code = this.elseMan.share_code
										this.roomName = this.elseMan.name
										this.roomGender = this.elseMan.gender
										// 等于0代表的是被分享人,两人的情况被分享人需要提示:1,是否确认和别人同住 2.等待分享人二次确认 3.已有房间

										if (auth.team_confirm == 0) {
											this.timeOut()
											uni.reLaunch({
												url: '/packageB/teamCheckIn/waitTeam?team_id=' + this
													.teamDetail.id + '&share_code=' + this.share_code
											})
										} else {
											if (auth.bill_id && (auth.user_stay_status == 1)) {
												console.log('hello0')
												this.haveRoom = true
											} else {
												console.log('woshi1', auth.authentication,auth.team_confirm, auth
													.confirm);
												if (auth.confirm != 0 && auth.bill_id && auth.authentication && (auth
														.user_stay_status == 1)) {
													//都有房间的状态提示分配房间了去房卡
													console.log('hello1')
													this.haveRoom = true
												} else if (((!auth.bill_id || (auth.bill_id && (auth
														.user_stay_status == 0 || !auth.user_stay_status)))) && auth
													.accept == 1 && auth.confirm == 1) {
													// 都没有房间，且我的confirm==1代表我已经确认并办理付款，但是对方还未确认
													console.log('hello2')
													this.awaitArgee = true

												}  else if (auth.confirm == 0 && auth.bill_id && auth
													.user_stay_status == 1 &&
													auth.accept == 1) {
													// 我有bill,需要提示是否和别人同住
													console.log('hello4')
													this.checkInShare = true
												} else if (auth.confirm == 2 && auth.bill_id && auth
													.user_stay_status == 0 && !
													auth.authentication && this.totalPrice == 0) {
													console.log('hello5')
													// 有房间没认证，提示弹框去认证
													this.toAuth = true

												} else if (auth.authentication == 1 && auth.confirm == 0 && (!auth
														.bill_id || (auth.bill_id && (auth.user_stay_status == 0 || !
															auth.user_stay_status))) && !this.agreeFlag && auth
													.accept == 1 && this.team_type != 2) {
													console.log('hello6')
													this.ifArgeeAuth = true
													let a = setInterval(item => {
														if (this.timeEsc <= 0) {
															clearInterval(a)
														} else {
															this.timeEsc--
														}
													}, 1000)
												} else if (auth.authentication == 0 && auth.confirm == 0 && (!auth
														.bill_id || (auth.bill_id && (auth.user_stay_status == 0 || !
															auth.user_stay_status))) && !this.agreeFlag && auth
													.accept == 1 && this.team_type != 2) {
													console.log('hello7')
													this.ifArgeeAuth = true
													let a = setInterval(item => {
														if (this.timeEsc <= 0) {
															clearInterval(a)
														} else {
															this.timeEsc--
														}
													}, 1000)
												} else if (auth.authentication == 1 && auth.team_confirm == -1 && auth
													.confirm == 2) {
													console.log('hello8')
													let time = this.shopSetting.filter(item => {
														return item.sign == 'enter_time'
													})[0].property.value

													let start = this.$moment(this.teamDetail
															.enter_time_plan * 1000).format('YYYY-MM-DD') +
														' ' + time + ':00'
													start = this.$moment(start, 'YYYY-MM-DD hh:mm:ss')
														.unix()

													let now = this.$moment().unix()
													console.log(now, start, this.teamDetail
														.leave_time_plan, 'kl');
													if (now < start || now > this.teamDetail
														.leave_time_plan) {
														console.log(now, start, this.teamDetail
															.leave_time_plan, 'kl1');
														this.earthTime = true
														return;

													} else {
														this.waitShow = true
													}

												}else if (auth.authentication == 0 && auth.team_confirm == -1 && auth
													.confirm == 2) {
													console.log('hello8')
													this.awaitArgee = false

												}
											}
										}

									}
								}
							}
						}
					})

			},
			noShareSure() {
				this.noShare = false
				this.share_code = null
			},

			chooseWx() {

				// this.$iBox.http('getSameRoomCode', {
				// 		team_id: this.team_id
				// 	})({
				// 		method: 'post'
				// 	}).then(res => {
				// 		this.shareShow = false
				// 		this.share_code = res.data.code
				// 		this.team_id = res.data.team_id
				// 		//查询订单信息
				// 		this.$iBox
				// 			.http('getTeamRoomBillInfo', this.params)({
				// 				method: 'post'
				// 			})
				// 			.then(res => {
				// 				uni.hideLoading()
				// 				this.teamDetail = res.data
				// 				let auth = res.data.users.filter(item => {
				// 					return item.common_code == this
				// 						.userInfo.common_code
				// 				})[0]
				// 				if (auth) {
				// 					this.user = auth
				// 					this.ifAuth = auth.authentication
				// 				}

				// 				// 获取另外一人的信息
				// 				this.elseMan = res.data.users.filter(item => {
				// 					return item.common_code != this
				// 						.userInfo.common_code
				// 				})[0]
				// 			})
				// 		})
			},

			chooseType() {
				if (this.type == 1) {
					this.type = 2
					uni.showModal({
						title: '提示',
						content: '选择面对面扫码，前面分享的微信链接会立即失效！',
						success: res => {
							if (res.confirm) {
								this.$iBox.http('getSameRoomCode', {
									team_id: this.team_id
								})({
									method: 'post'
								}).then(res => {
									this.shareShow = false
									this.share_code = res.data.code
									// this.team_id = res.data.team_id
									//查询订单信息
									this.$iBox
										.http('getTeamRoomBillInfo', this.params)({
											method: 'post'
										})
										.then(res => {
											uni.hideLoading()
											this.teamDetail = res.data
											let auth = res.data.users.filter(item => {
												return item.common_code == this.userInfo
													.common_code
											})[0]
											if (auth) {
												this.user = auth
												this.ifAuth = auth.authentication
											}


											// 获取另外一人的信息
											this.elseMan = res.data.users.filter(item => {
												return item.common_code != this.userInfo
													.common_code
											})[0]
										})
								})
							} else {
								this.type = 1
							}

						}
					})
				} else {
					this.type = 1

				}


				// uni.setStorageSync('type', 1);
			},
			async checkInToWait() {
				let time = this.shopSetting.filter(item => {
					return item.sign == 'enter_time'
				})[0].property.value

				let start = this.$moment(this.teamDetail.enter_time_plan * 1000).format('YYYY-MM-DD') + ' ' + time +
					':00'
				start = this.$moment(start, 'YYYY-MM-DD hh:mm:ss').unix()

				let now = this.$moment().unix()
				console.log(now, start, this.teamDetail.leave_time_plan, 'kl');
				if (now < start || now > this.teamDetail.leave_time_plan) {
					console.log(now, start, this.teamDetail.leave_time_plan, 'kl1');
					this.earthTime = true
					return;
				} else {
					uni.showModal({
						title: '提示',
						content: '是否确认立即入住？',
						success: res => {
							if (res.confirm) {
								this.timeOut()
								this.elseDefine = false
								this.elseNoShare = false
								this.noShareAuth = false
								try {
									this.blueGet()
										.then(hasValidDevice => {
											if (hasValidDevice) {
												console.log('检测到有效设备，继续执行...');
												let room = this.teamDetail.users.filter(item => {
													return item.common_code == this.userInfo
														.common_code
												})[0]
												//允许脏房入住,允许换房1 ,不允许0
												if (this.teamDetail.is_dirty_room_change == 1) {
													if (this.teamDetail.dirty_check_in == 1 && room &&
														room
														.clean_status == 0) {
														uni.showModal({
															title: '提示',
															content: '当前房间是脏房,是否继续入住',
															cancelText: '继续入住',
															confirmText: '更换房间',
															success: res => {
																if (res.confirm) {
																	// 更换房间
																	this.$iBox
																		.http(
																			'changeRoomCheckIn', {
																				teamId: this
																					.team_id
																			})({
																			method: 'post'
																		})
																		.then(
																			res => {
																				uni.reLaunch({
																					url: '/pages/myRoom/myRoom'
																				})
																			})
																} else {
																	this.$iBox
																		.http(
																			'teamCheckIn', {
																				teamId: this
																					.team_id
																			})({
																			method: 'post'
																		})
																		.then(
																			res => {
																				uni.reLaunch({
																					url: '/pages/myRoom/myRoom'
																				})
																			})
																}
															}
														})


													} else {
														this.$iBox
															.http('teamCheckIn', {
																teamId: this.team_id
															})({
																method: 'post'
															})
															.then(res => {
																uni.reLaunch({
																	url: '/pages/myRoom/myRoom'
																})
															}).catch(err => {
																if (err.includes('清洁')) {
																	uni.showModal({
																		title: '提示',
																		content: '当前是脏房、是否更换其他同房型其他干净房',
																		cancelText: '等待清洁',
																		confirmText: '更换房间',
																		success: res => {
																			if (res
																				.confirm
																			) {
																				// 更换房间
																				this.$iBox
																					.http(
																						'changeRoomCheckIn', {
																							teamId: this
																								.team_id
																						}
																					)
																					({
																						method: 'post'
																					})
																					.then(
																						res => {
																							uni.reLaunch({
																								url: '/pages/myRoom/myRoom'
																							})
																						}
																					)
																			} else {

																			}
																		}
																	})
																}


															})
													}
												} else {
													if (this.teamDetail.dirty_check_in == 1 && room &&
														room
														.clean_status == 0) {
														uni.showModal({
															title: '提示',
															content: '当前房间是脏房,是否继续入住',
															cancelText: '继续入住',
															confirmText: '等待清洁',
															success: res => {
																if (res.confirm) {
																	// 等待清洁

																} else {
																	this.$iBox
																		.http(
																			'teamCheckIn', {
																				teamId: this
																					.team_id
																			})({
																			method: 'post'
																		})
																		.then(
																			res => {
																				uni.reLaunch({
																					url: '/pages/myRoom/myRoom'
																				})
																			})
																}
															}
														})


													} else {
														this.$iBox
															.http('teamCheckIn', {
																teamId: this.team_id
															})({
																method: 'post'
															})
															.then(res => {
																uni.reLaunch({
																	url: '/pages/myRoom/myRoom'
																})
															}).catch(err => {
																if (err.includes('清洁')) {
																	uni.showModal({
																		title: '提示',
																		content: '当前是脏房、请等待清洁完成',
																		showCancel: false,
																		confirmText: '等待清洁',
																		success: res => {

																		}
																	})
																}


															})
													}
												}


											} else {
												this.showDevice = true;
											}
										})
										.catch(error => {
											console.error('签到前检查失败:', error);
											this.showDevice = true;
										});

								} catch (error) {
									console.error('签到前检查失败:', error);
									this.showDevice = true;
								}

							}
						}
					})

				}
			},

			checkInTo() {
				let time = this.shopSetting.filter(item => {
					return item.sign == 'enter_time'
				})[0].property.value

				let start = this.$moment(this.teamDetail.enter_time_plan * 1000).format('YYYY-MM-DD') + ' ' + time + ':00'
				start = this.$moment(start, 'YYYY-MM-DD hh:mm:ss').unix()

				let now = this.$moment().unix()
				console.log(now, start, this.teamDetail.leave_time_plan, 'kl');
				if (now < start || now > this.teamDetail.leave_time_plan) {
					console.log(now, start, this.teamDetail.leave_time_plan, 'kl1');
					this.earthTime = true
					return;
				} else {
					uni.showModal({
						title: '提示',
						content: '系统即将为自动分配，是否确认立即入住？',
						success: res => {
							if (res.confirm) {
								this.checkIn()
							}
						}
					})

				}

			},

			chooseSc() {
				this.type = 2
				this.shareShow = false
				this.$iBox.http('getSameRoomCode', {
					team_id: this.team_id
				})({
					method: 'post'
				}).then(res => {
					this.share_code = res.data.code
					// this.team_id = res.data.team_id
					//查询订单信息
					this.$iBox
						.http('getTeamRoomBillInfo', this.params)({
							method: 'post'
						})
						.then(res => {
							uni.hideLoading()
							this.teamDetail = res.data
							let auth = res.data.users.filter(item => {
								return item.common_code == this
									.userInfo.common_code
							})[0]
							if (auth) {
								this.user = auth
								this.ifAuth = auth.authentication
							}

							// 获取另外一人的信息
							this.elseMan = res.data.users.filter(item => {
								return item.common_code != this
									.userInfo.common_code
							})[0]
						})
				})

			},
			changeType() {
				// this.shareShow = true

			},
			scan() {
				wx.scanCode({
					onlyFromCamera: true,
					success: (res) => {
						console.log(res, 'scan')
						let sence = res.path.split('?')[1].split('scene=')[1]

						if (sence.split('&').length > 1) {
							this.team_id = sence.split('&')[1].split('=')[1]
							this.share_code = sence.split('&')[0].split('=')[1]
						} else {
							sence = decodeURIComponent(sence)
							this.team_id = sence.split('&')[1].split('=')[1]
							this.share_code = sence.split('&')[0].split('=')[1]
						}
						// sence = decodeURIComponent(sence)
						console.log(sence, 'sence');


						let params = {
							team_id: this.team_id,
							share_code: this.share_code,
							confirm: 1
						}

						this.$iBox.http('confirmTeam', params)({
								method: 'post'
							})
							.then(res => {
								this.params.team_id = this.team_id
								this.params.share_code = this.share_code
								this.$iBox
									.http('getTeamRoomBillInfo', this.params)({
										method: 'post'
									})
									.then(res => {
										uni.hideLoading()
										this.teamDetail = res.data
										let auth = res.data.users.filter(item => {
											return item.common_code == this
												.userInfo.common_code
										})[0]
										if (auth) {
											this.user = auth
											this.ifAuth = auth.authentication
										}

										// 获取另外一人的信息
										this.elseMan = res.data.users.filter(item => {
											return item.common_code != this.userInfo
												.common_code
										})[0]
									})
							})
						this.params.team_id = this.team_id
						this.params.share_code = this.share_code
						let self = this
						this.timeInfo && clearInterval(this.timeInfo)
						this.timeInfo1 && clearInterval(this.timeInfo1)
						this.timeInfo1 = setInterval((function target() {
							self.getTeamInfo()
							return target
						})(), 5000)
					}
				})
			},
			to_auth() {
				uni.navigateTo({
					url: '/packageB/teamCheckIn/confirmTeam?team_id=' + this
						.teamDetail.id + '&startTime=' + this.teamDetail.enter_time_plan + '&endTime=' + this
						.teamDetail.leave_time_plan
				})

			},
			sureInfo() {

				uni.showModal({
					title: '提示',
					content: ' 为了您更快捷地完成入住，需要采集您的身份证信息并进行人像比对，请问是否允许？',
					confirmText: '是',
					cancelText: '否',
					success: res => {
						if (res.confirm) {
							uni.showLoading({
								title: '确认团队中...'
							})
							let params = {}
							if (this.share_code) {
								params = {
									team_id: this.teamDetail.id,
									share_code: this.share_code
								}
							} else if (this.single) {
								params = {
									team_id: this.teamDetail.id,
									single: this.single
								}
							} else {
								params = {
									team_id: this.teamDetail.id
								}
							}


							if (this.room_id) {
								params.bill_id = this.room_id
							}

							this.$iBox.http('confirmTeam', params)({
									method: 'post'
								})
								.then(res => {

									if (res.data.team_type != 2) {
										if (this.share_code) {
											let params = {
												team_id: this.teamDetail.id,
												share_code: this.share_code,
												confirm: this.if_tong ? 1 : -1
											}

											this.$iBox.http('chooseStayUser', params)({
													method: 'post'
												})
												.then(res => {
													// this.teamDetail = res.data
													this.userList = res.data.users
													let auth = res.data.users.filter(item => {
														return item.common_code == this
															.userInfo.common_code
													})[0]
													if (auth) {
														this.user = auth
														this.ifAuth = auth.authentication
														uni.hideLoading()
														if (auth.identification_image) {
															this.idCard_img = auth.identification_image
															this.id_number = auth.identification_number
															this.name = auth.name
														}

													}
												})
										} else {
											let params = {
												team_id: this.teamDetail.id,
												share_code: null,
												confirm: this.manNum == 1 ? 1 : 0
											}

											this.$iBox.http('chooseStayUser', params)({
													method: 'post'
												})
												.then(res => {
													// this.teamDetail = res.data
													this.userList = res.data.users
													let auth = res.data.users.filter(item => {
														return item.common_code == this
															.userInfo.common_code
													})[0]
													if (auth) {
														this.user = auth
														this.ifAuth = auth.authentication
														uni.hideLoading()
														if (auth.identification_image) {
															this.idCard_img = auth.identification_image
															this.id_number = auth.identification_number
															this.name = auth.name
														}

													}
												})
										}
									}

									this.$iBox.http('getPayAmount', {
											team_id: this.teamDetail.id
										})({
											method: 'post'
										})
										.then(res1 => {

											this.cashNum = res1.data.cash_pledge
											this.roomPrices = res1.data.room_price
											this.totalPrice = res1.data.total_amount
											this.already_pay_amount = res1.data.already_pay_amount

											if (res.data.users.length > 0) {
												this.scanExc()
											} else {
												uni.showModal({
													title: '扫码失败!',
													content: '请重新扫码',
													showCancel: false,
													success: res => {
														if (res.confirm) {
															//直接退出小程序
															wx.exitMiniProgram()
														}
													}
												})
											}

										})

								}).catch(err => {
									uni.showModal({
										title: '提示',
										content: err,
										showCancel: false,
										success: res => {
											if (res.confirm) {

											}
										}
									})
								})
						} else {
							uni.showModal({
								title: '提示',
								content: '请到前台领取钥匙!',
								showCancel: false,
								success: res => {
									uni.exitMiniProgram()
								}
							})
						}
					}
				})

			},
			noShareSureAuth() {
				this.noShareAuth = false
			},

			toDefine() {
				this.checkInShare = false
				let params = {
					team_id: this.teamDetail.id,
					share_code: this.share_code,
					confirm: -1
				}
				this.$iBox.http('chooseStayUser', params)({
						method: 'post'
					})
					.then(res => {

					})

			},
			scanCode() {
				wx.scanCode({
					onlyFromCamera: true,
					success: (res) => {

					}
				})
			},
			async toAgree() {
				this.checkInShare = false
				// 订单状态等于4状态下
				this.$iBox.http('staySameRoom', {
						team_id: this.teamDetail.id,
						share_code: this.share_code
					})({
						method: 'post'
					})
					.then(res => {
						try {

							this.blueGet()
								.then(hasValidDevice => {
									if (hasValidDevice) {


										let room = this.teamDetail.users.filter(item => {
											return item.common_code == this.userInfo.common_code
										})[0]
										//允许脏房入住,允许换房1 ,不允许0
										if (this.teamDetail.is_dirty_room_change == 1) {
											if (this.teamDetail.dirty_check_in == 1 && room && room
												.clean_status ==
												0) {
												uni.showModal({
													title: '提示',
													content: '当前房间是脏房,是否继续入住',
													cancelText: '继续入住',
													confirmText: '更换房间',
													success: res => {
														if (res.confirm) {
															// 更换房间
															this.$iBox
																.http(
																	'changeRoomCheckIn', {
																		teamId: this
																			.team_id
																	})({
																	method: 'post'
																})
																.then(
																	res => {
																		uni.reLaunch({
																			url: '/pages/myRoom/myRoom'
																		})
																	})
														} else {
															this.$iBox
																.http(
																	'teamCheckIn', {
																		teamId: this
																			.team_id
																	})({
																	method: 'post'
																})
																.then(
																	res => {
																		uni.reLaunch({
																			url: '/pages/myRoom/myRoom'
																		})
																	})
														}
													}
												})


											} else {
												this.$iBox
													.http('teamCheckIn', {
														teamId: this.team_id
													})({
														method: 'post'
													})
													.then(res => {
														if (auth.team_confirm == 0) {
															uni.reLaunch({
																url: '/packageB/teamCheckIn/waitTeam?team_id=' +
																	this
																	.teamDetail.id
															})
														}
													}).catch(err => {
														if (err.includes('清洁')) {
															uni.showModal({
																title: '提示',
																content: '当前是脏房、是否更换其他同房型其他干净房',
																cancelText: '等待清洁',
																confirmText: '更换房间',
																success: res => {
																	if (res.confirm) {
																		// 更换房间
																		this.$iBox
																			.http(
																				'changeRoomCheckIn', {
																					teamId: this
																						.team_id
																				})({
																				method: 'post'
																			})
																			.then(
																				res => {
																					uni.reLaunch({
																						url: '/pages/myRoom/myRoom'
																					})
																				})
																	} else {

																	}
																}
															})
														}


													})
											}
										} else {
											if (this.teamDetail.dirty_check_in == 1 && room && room
												.clean_status ==
												0) {
												uni.showModal({
													title: '提示',
													content: '当前房间是脏房,是否继续入住',
													cancelText: '继续入住',
													confirmText: '等待清洁',
													success: res => {
														if (res.confirm) {

														} else {
															this.$iBox
																.http(
																	'teamCheckIn', {
																		teamId: this
																			.team_id
																	})({
																	method: 'post'
																})
																.then(
																	res => {
																		uni.reLaunch({
																			url: '/pages/myRoom/myRoom'
																		})
																	})
														}
													}
												})


											} else {
												this.$iBox
													.http('teamCheckIn', {
														teamId: this.team_id
													})({
														method: 'post'
													})
													.then(res => {
														if (auth.team_confirm == 0) {
															uni.reLaunch({
																url: '/packageB/teamCheckIn/waitTeam?team_id=' +
																	this
																	.teamDetail.id
															})
														}
													}).catch(err => {
														if (err.includes('清洁')) {
															uni.showModal({
																title: '提示',
																content: '当前是脏房,请等待清洁完成',
																showCancel: false,
																confirmText: '等待清洁',
																success: res => {

																}
															})
														}


													})
											}
										}


									} else {
										this.showDevice = true;
									}
								})
								.catch(error => {
									console.error('签到前检查失败:', error);
									this.showDevice = true;
								});

						} catch (error) {
							console.error('签到前检查失败:', error);
							this.showDevice = true;
						}


					})
			},
			goMain() {
				uni.reLaunch({
					url: '/pages/index/index'
				})
			},
			exit() {
				this.timeOutShow = false
				uni.exitMiniProgram()
			},
			toTalk() {
				this.linkShow = false
			},
			toSure() {
				// 手动查询

				this.awaitArgee = false
			},
			back() {
				this.noDetail = false
				uni.exitMiniProgram({})
			},
			againShare() {
				this.elseDefine = false
				this.elseNoShare = false
				this.noShareAuth = false
				this.shereConfirmAuto = false
				this.shareShow = true
				this.defindFlagHave = 1
				this.type = 1
			},
			againShareHave() {
				this.elseDefine = false
				this.elseNoShareHave = false
				this.noShareAuth = false
				this.shareShow = true
				this.defindFlag = 1
				this.type = 1
			},
			closeShereConfirm() {
				this.shereConfirm = false
			},
			autoCheckIn() {
				this.timeOut()
				uni.showModal({
					title: '提示',
					content: '是否立即自动分配',
					success: res => {
						if (res.confirm) {
							try {
								this.blueGet()
									.then(hasValidDevice => {
										if (hasValidDevice) {

											this.$iBox.http('cancelShare', {
												team_id: this.team_id
											})({
												method: 'post'
											}).then(res => {

												this.$iBox
													.http('getTeamRoomBillInfo', this.params)({
														method: 'post'
													})
													.then(res => {
														let auth = res.data.users.filter(
															item => {
																return item.common_code ==
																	this
																	.userInfo.common_code
															})[0]

														if (auth.team_confirm == 0) {
															uni.reLaunch({
																url: '/packageB/teamCheckIn/waitTeam?team_id=' +
																	this.teamDetail.id
															})
														} else {
															if (auth.bill_id && (auth
																	.user_stay_status == 1)) {
																this.haveRoom = true
															}
														}

													})

											})
										} else {
											this.showDevice = true;
										}
									})
									.catch(error => {
										console.error('签到前检查失败:', error);
										this.showDevice = true;
									});

							} catch (error) {
								console.error('签到前检查失败:', error);
								this.showDevice = true;
							}

						}
					}
				})

			},
			checkIn() {
				this.timeOut()
				// this.shareShow = false
				this.$iBox.http('cancelShare', {
					team_id: this.team_id
				})({
					method: 'post'
				}).then(res => {
					this.elseDefine = false
					this.elseNoShare = false
					this.noShareAuth = false
					this.blueGet()
						.then(hasValidDevice => {
							if (hasValidDevice) {
								let room = this.teamDetail.users.filter(item => {
									return item.common_code == this.userInfo.common_code
								})[0]
								//允许脏房入住,允许换房1 ,不允许0
								if (this.teamDetail.is_dirty_room_change == 1) {
									if (this.teamDetail.dirty_check_in == 1 && room && room.clean_status == 0) {
										uni.showModal({
											title: '提示',
											content: '当前房间是脏房,是否继续入住',
											cancelText: '继续入住',
											confirmText: '更换房间',
											success: res => {
												if (res.confirm) {
													// 更换房间
													this.$iBox
														.http(
															'changeRoomCheckIn', {
																teamId: this
																	.team_id
															})({
															method: 'post'
														})
														.then(
															res => {
																uni.reLaunch({
																	url: '/pages/myRoom/myRoom'
																})
															})
												} else {
													this.$iBox
														.http(
															'teamCheckIn', {
																teamId: this
																	.team_id
															})({
															method: 'post'
														})
														.then(
															res => {
																uni.reLaunch({
																	url: '/pages/myRoom/myRoom'
																})
															})
												}
											}
										})
									
									
									} else {
										this.$iBox
											.http('teamCheckIn', {
												teamId: this.team_id
											})({
												method: 'post'
											})
											.then(res => {
												this.$iBox
													.http('getTeamRoomBillInfo', this.params)({
														method: 'post'
													})
													.then(res => {
														let auth = res.data.users.filter(item => {
															return item.common_code == this
																.userInfo.common_code
														})[0]
									
														if (auth.team_confirm == 0) {
															uni.reLaunch({
																url: '/packageB/teamCheckIn/waitTeam?team_id=' +
																	this
																	.teamDetail.id
															})
														} else {
															if (auth.bill_id && (auth.user_stay_status ==
																	1)) {
																this.haveRoom = true
															}
														}
									
													})
											}).catch(err => {
												if (err.includes('清洁')) {
													uni.showModal({
														title: '提示',
														content: '当前是脏房、是否更换其他同房型其他干净房',
														cancelText: '等待清洁',
														confirmText: '更换房间',
														success: res => {
															if (res.confirm) {
																// 更换房间
																this.$iBox
																	.http(
																		'changeRoomCheckIn', {
																			teamId: this
																				.team_id
																		})({
																		method: 'post'
																	})
																	.then(
																		res => {
																			uni.reLaunch({
																				url: '/pages/myRoom/myRoom'
																			})
																		})
															} else {
									
															}
														}
													})
												}
									
									
											})
									}
								} else {
									if (this.teamDetail.dirty_check_in == 1 && room && room.clean_status == 0) {
										uni.showModal({
											title: '提示',
											content: '当前房间是脏房,是否继续入住',
											cancelText: '继续入住',
											confirmText: '等待清洁',
											success: res => {
												if (res.confirm) {
													
												} else {
													this.$iBox
														.http(
															'teamCheckIn', {
																teamId: this
																	.team_id
															})({
															method: 'post'
														})
														.then(
															res => {
																uni.reLaunch({
																	url: '/pages/myRoom/myRoom'
																})
															})
												}
											}
										})
									
									
									} else {
										this.$iBox
											.http('teamCheckIn', {
												teamId: this.team_id
											})({
												method: 'post'
											})
											.then(res => {
												this.$iBox
													.http('getTeamRoomBillInfo', this.params)({
														method: 'post'
													})
													.then(res => {
														let auth = res.data.users.filter(item => {
															return item.common_code == this
																.userInfo.common_code
														})[0]
									
														if (auth.team_confirm == 0) {
															uni.reLaunch({
																url: '/packageB/teamCheckIn/waitTeam?team_id=' +
																	this
																	.teamDetail.id
															})
														} else {
															if (auth.bill_id && (auth.user_stay_status ==
																	1)) {
																this.haveRoom = true
															}
														}
									
													})
											}).catch(err => {
												if (err.includes('清洁')) {
													uni.showModal({
														title: '提示',
														content: '当前是脏房、请等待清洁完成!',
														showCancel:false,
														confirmText: '等待清洁',
														success: res => {
															
														}
													})
												}
									
									
											})
									}
								}


							} else {
								this.showDevice = true;
							}
						})
						.catch(error => {
							console.error('签到前检查失败:', error);
							this.showDevice = true;
						});



				})
			},
			elseTimecheckInAgain(){
				this.$iBox.http('confirmSame', {
					team_id: this.team_id,
					confirm: 2
				})({
					method: 'post'
				}).then(res => {
					this.againSure = false
					this.earthTime = true
				})
			},
			checkInAgain() {
				this.timeOut()
				this.$iBox.http('confirmSame', {
					team_id: this.team_id,
					confirm: 2
				})({
					method: 'post'
				}).then(res => {
					this.againSure = false
					this.blueGet()
						.then(hasValidDevice => {
							if (hasValidDevice) {

								let room = this.teamDetail.users.filter(item => {
									return item.common_code == this.userInfo.common_code
								})[0]
								//允许脏房入住,允许换房1 ,不允许0
								if (this.teamDetail.is_dirty_room_change == 1) { 
									if (this.teamDetail.dirty_check_in == 1 && room && room.clean_status == 0) {
										uni.showModal({
											title: '提示',
											content: '当前房间是脏房,是否继续入住',
											cancelText: '继续入住',
											confirmText: '更换房间',
											success: res => {
												if (res.confirm) {
													// 更换房间
													this.$iBox
														.http(
															'changeRoomCheckIn', {
																teamId: this
																	.team_id
															})({
															method: 'post'
														})
														.then(
															res => {
																uni.reLaunch({
																	url: '/pages/myRoom/myRoom'
																})
															})
												} else {
													this.$iBox
														.http(
															'teamCheckIn', {
																teamId: this
																	.team_id
															})({
															method: 'post'
														})
														.then(
															res => {
																uni.reLaunch({
																	url: '/pages/myRoom/myRoom'
																})
															})
												}
											}
										})
									
									
									} else {
										this.$iBox
											.http('teamCheckIn', {
												teamId: this.team_id
											})({
												method: 'post'
											})
											.then(res => {
												let auth = res.data.users.filter(item => {
													return item.common_code == this.userInfo
														.common_code
												})[0]
									
												if (auth.team_confirm == 0) {
													uni.reLaunch({
														url: '/packageB/teamCheckIn/waitTeam?team_id=' +
															this.teamDetail.id +
															'&share_code=' + this
															.share_code
													})
												} else {
													if (auth.bill_id && (auth.user_stay_status == 1)) {
														this.haveRoom = true
													}
												}
											}).catch(err => {
												if (err.includes('清洁')) {
													uni.showModal({
														title: '提示',
														content: '当前是脏房、是否更换其他同房型其他干净房',
														cancelText: '等待清洁',
														confirmText: '更换房间',
														success: res => {
															if (res.confirm) {
																// 更换房间
																this.$iBox
																	.http(
																		'changeRoomCheckIn', {
																			teamId: this
																				.team_id
																		})({
																		method: 'post'
																	})
																	.then(
																		res => {
																			uni.reLaunch({
																				url: '/pages/myRoom/myRoom'
																			})
																		})
															} else {
									
															}
														}
													})
												}
									
									
											})
									}
									
								}else {
									if (this.teamDetail.dirty_check_in == 1 && room && room.clean_status == 0) {
										uni.showModal({
											title: '提示',
											content: '当前房间是脏房,是否继续入住',
											cancelText: '继续入住',
											confirmText: '等待清洁',
											success: res => {
												if (res.confirm) {
													
												} else {
													this.$iBox
														.http(
															'teamCheckIn', {
																teamId: this
																	.team_id
															})({
															method: 'post'
														})
														.then(
															res => {
																uni.reLaunch({
																	url: '/pages/myRoom/myRoom'
																})
															})
												}
											}
										})
									
									
									} else {
										this.$iBox
											.http('teamCheckIn', {
												teamId: this.team_id
											})({
												method: 'post'
											})
											.then(res => {
												let auth = res.data.users.filter(item => {
													return item.common_code == this.userInfo
														.common_code
												})[0]
									
												if (auth.team_confirm == 0) {
													uni.reLaunch({
														url: '/packageB/teamCheckIn/waitTeam?team_id=' +
															this.teamDetail.id +
															'&share_code=' + this
															.share_code
													})
												} else {
													if (auth.bill_id && (auth.user_stay_status == 1)) {
														this.haveRoom = true
													}
												}
											}).catch(err => {
												if (err.includes('清洁')) {
													uni.showModal({
														title: '提示',
														content: '当前是脏房、请等待清洁完成!',
														showCancel:false,
														confirmText: '等待清洁',
														success: res => {
															
														}
													})
												}
									
									
											})
									}
									
								}
							
							} else {
								this.showDevice = true;
							}
						})
						.catch(error => {
							console.error('签到前检查失败:', error);
							this.showDevice = true;
						});


				})
			},
			againShareTo() {

				this.$iBox.http('confirmSame', {
					team_id: this.team_id,
					confirm: 0
				})({
					method: 'post'
				}).then(res => {



					this.shareShow = true
					this.againSure = false
					this.type = 1

				}).catch(err => {
					uni.showToast({
						icon: 'none',
						title: this.team_id + '分享失败'
					})
				})
			},
			checkInAwait() {
				this.blueGet()
					.then(hasValidDevice => {
						if (hasValidDevice) {

							let room = this.teamDetail.users.filter(item => {
								return item.common_code == this.userInfo.common_code
							})[0]
							//允许脏房入住,允许换房1 ,不允许0
							if (this.teamDetail.is_dirty_room_change == 1) {
								//允许脏房入住
								if (this.teamDetail.dirty_check_in == 1 && room && room.clean_status == 0) {
									uni.showModal({
										title: '提示',
										content: '当前房间是脏房,是否继续入住',
										cancelText: '继续入住',
										confirmText: '更换房间',
										success: res => {
											if (res.confirm) {
												// 更换房间
												this.$iBox
													.http(
														'changeRoomCheckIn', {
															teamId: this
																.team_id
														})({
														method: 'post'
													})
													.then(
														res => {
															uni.reLaunch({
																url: '/pages/myRoom/myRoom'
															})
														})
											} else {
												this.$iBox
													.http(
														'teamCheckIn', {
															teamId: this
																.team_id
														})({
														method: 'post'
													})
													.then(
														res => {
															uni.reLaunch({
																url: '/pages/myRoom/myRoom'
															})
														})
											}
										}
									})
								
								
								} else {
									this.$iBox
										.http('teamCheckIn', {
											teamId: this.team_id
										})({
											method: 'post'
										})
										.then(res => {
											this.checkInMain = false
											this.waitShow = false
											uni.reLaunch({
												url: '/pages/myRoom/myRoom'
											})
										}).catch(err => {
											if (err.includes('清洁')) {
												uni.showModal({
													title: '提示',
													content: '当前是脏房、是否更换其他同房型其他干净房',
													cancelText: '等待清洁',
													confirmText: '更换房间',
													success: res => {
														if (res.confirm) {
															// 更换房间
															this.$iBox
																.http(
																	'changeRoomCheckIn', {
																		teamId: this
																			.team_id
																	})({
																	method: 'post'
																})
																.then(
																	res => {
																		uni.reLaunch({
																			url: '/pages/myRoom/myRoom'
																		})
																	})
														} else {
								
														}
													}
												})
											}
								
								
										})
								}
							}else{
								//允许脏房入住
								if (this.teamDetail.dirty_check_in == 1 && room && room.clean_status == 0) {
									uni.showModal({
										title: '提示',
										content: '当前房间是脏房,是否继续入住',
										cancelText: '继续入住',
										confirmText: '等待清洁',
										success: res => {
											if (res.confirm) {
												
											} else {
												this.$iBox
													.http(
														'teamCheckIn', {
															teamId: this
																.team_id
														})({
														method: 'post'
													})
													.then(
														res => {
															uni.reLaunch({
																url: '/pages/myRoom/myRoom'
															})
														})
											}
										}
									})
								
								
								} else {
									this.$iBox
										.http('teamCheckIn', {
											teamId: this.team_id
										})({
											method: 'post'
										})
										.then(res => {
											this.checkInMain = false
											this.waitShow = false
											uni.reLaunch({
												url: '/pages/myRoom/myRoom'
											})
										}).catch(err => {
											if (err.includes('清洁')) {
												uni.showModal({
													title: '提示',
													content: '当前是脏房、请等待清洁完成',
													showCancel:false,
													success: res => {
														
													}
												})
											}
								
								
										})
								}
							}
							
						} else {
							this.showDevice = true;
						}
					})
					.catch(error => {
						console.error('签到前检查失败:', error);
						this.showDevice = true;
					});


			},

			haveMan() {
				this.manNum = 1
				this.ifHaveMan = false
				this.haveFlag = 1

			},
			noMan() {
				this.manNum = 0
				this.ifHaveMan = false
				this.haveFlag = 1
			},
			haveManAuth() {
				this.haveFlag = 1
				this.ifHaveManAuth = false
				let params = {
					team_id: this.team_id,
					share_code: null,
					confirm: 1
				}

				this.$iBox.http('chooseStayUser', params)({
						method: 'post'
					})
					.then(res => {
						this.params.team_id = this.team_id
						this.params.share_code = this.share_code
						this.$iBox.http('getTeamRoomBillInfo', this.params)({
							method: 'post'
						}).then(res => {
							uni.hideLoading()
							this.teamDetail = res.data
							let auth = res.data.users.filter(item => {
								return item.common_code == this.userInfo.common_code
							})[0]
							if (auth) {
								this.user = auth
								this.ifAuth = auth.authentication
							}

							// 获取另外一人的信息
							this.elseMan = res.data.users.filter(item => {
								return item.common_code != this.userInfo.common_code
							})[0]
						})
					})
			},

			noManAuth() {
				this.haveFlag = 1
				this.ifHaveManAuth = false
				let params = {
					team_id: this.team_id,
					share_code: null,
					confirm: 0
				}

				this.$iBox.http('chooseStayUser', params)({
						method: 'post'
					})
					.then(res => {
						this.params.team_id = this.team_id
						this.params.share_code = this.share_code
						this.$iBox
							.http('getTeamRoomBillInfo', this.params)({
								method: 'post'
							})
							.then(res => {
								uni.hideLoading()
								this.teamDetail = res.data
								let auth = res.data.users.filter(item => {
									return item.common_code == this
										.userInfo.common_code
								})[0]
								if (auth) {
									this.user = auth
									this.ifAuth = auth.authentication
								}

								// 获取另外一人的信息
								this.elseMan = res.data.users.filter(item => {
									return item.common_code != this.userInfo
										.common_code
								})[0]
							})
					})
			},
			argee() {
				if (this.timeEsc == 0) {
					this.if_tong = true
					this.ifArgee = false

					this.agreeFlag = 1
				}
			},
			define() {

				this.agreeFlag = 1
				let params = {
					team_id: this.teamDetail.id,
					share_code: this.share_code,
					confirm: this.if_tong ? 1 : -1
				}

				this.$iBox.http('chooseStayUser', params)({
						method: 'post'
					})
					.then(res => {
						this.if_tong = false
						this.ifArgee = false
					})
				uni.showModal({
					title: '提示',
					content: '您已拒绝分享邀请，请去酒店前台扫码办理',
					showCancel: false,
					success: res => {
						if (res.confirm) {
							//直接退出小程序
							wx.exitMiniProgram()
						}
					}
				})

			},
			argeeAuth() {
				if (this.timeEsc == 0) {
					let params = {
						team_id: this.teamDetail.id,
						share_code: this.share_code,
						confirm: 1
					}

					this.$iBox.http('chooseStayUser', params)({
							method: 'post'
						})
						.then(res => {
							this.if_tong = true
							this.ifArgeeAuth = false
							this.agreeFlag = 1
						})
				}
			},
			defineAuth() {

				let params = {
					team_id: this.teamDetail.id,
					share_code: this.share_code,
					confirm: this.if_tong ? 1 : -1
				}

				this.$iBox.http('chooseStayUser', params)({
						method: 'post'
					})
					.then(res => {
						this.if_tong = false
						this.ifArgeeAuth = false
						this.agreeFlag = 1
					})
				uni.showModal({
					title: '提示',
					content: '您已拒绝分享邀请，请去酒店前台扫码办理',
					showCancel: false,
					success: res => {
						if (res.confirm) {
							//直接退出小程序
							wx.exitMiniProgram()
						}
					}
				})

			},
			// 去房卡页
			toRoom() {
				this.haveRoom = false
				uni.reLaunch({
					url: '/pages/myRoom/myRoom'
				})
			},
			makePhone() {
				uni.makePhoneCall({
					phoneNumber: this.teamDetail.link_phone
				})
			},
			searchLast() {
				this.getTeamInfo()
			},

			cancelShow() {

				uni.showModal({
					title: '提示',
					content: '取消分享后系统将随机为您单独分配房间，是否继续?',
					confirmText: '重新分享',
					cancelText: '不分享了',
					success: res => {
						if (res.confirm) {
							this.type = 1
						} else {
							// this.shareShow = false

							this.$iBox.http('cancelShare', {
								team_id: this.team_id
							})({
								method: 'post'
							}).then(res => {


								uni.showModal({
									title: '提示',
									content: '已为您取消分享，是否立即自动分配房间!',
									success: res => {
										this.blueGet()
											.then(hasValidDevice => {
												if (hasValidDevice) {
													console.log('检测到有效设备，继续执行...');

													let room = this.teamDetail.users
														.filter(item => {
															return item
																.common_code ==
																this.userInfo
																.common_code
														})[0]
													//允许脏房入住,允许换房1 ,不允许0
													if (this.teamDetail.is_dirty_room_change == 1) { 
														if (this.teamDetail
															.dirty_check_in == 1 && room &&
															room.clean_status == 0) {
															uni.showModal({
																title: '提示',
																content: '当前房间是脏房,是否继续入住',
																cancelText: '继续入住',
																confirmText: '更换房间',
																success: res => {
																	if (res
																		.confirm
																	) {
																		// 更换房间
																		this.$iBox
																			.http(
																				'changeRoomCheckIn', {
																					teamId: this
																						.team_id
																				}
																			)
																			({
																				method: 'post'
																			})
																			.then(
																				res => {
																					uni.reLaunch({
																						url: '/pages/myRoom/myRoom'
																					})
																				}
																			)
																	} else {
																		this.$iBox
																			.http(
																				'teamCheckIn', {
																					teamId: this
																						.team_id
																				}
																			)
																			({
																				method: 'post'
																			})
																			.then(
																				res => {
																					uni.reLaunch({
																						url: '/pages/myRoom/myRoom'
																					})
																				}
																			)
																	}
																}
															})
														
														
														} else {
															this.$iBox
																.http('teamCheckIn', {
																	teamId: this
																		.team_id
																})({
																	method: 'post'
																})
																.then(res => {
																	this.$iBox.http(
																		'getTeamRoomBillInfo',
																		this
																		.params)({
																		method: 'post'
																	}).then(
																		res => {
																			let auth =
																				res
																				.data
																				.users
																				.filter(
																					item => {
																						return item
																							.common_code ==
																							this
																							.userInfo
																							.common_code
																					}
																				)[
																					0
																				]
														
																			if (auth
																				.team_confirm ==
																				0
																			) {
																				uni.navigateTo({
																					url: '/packageB/teamCheckIn/waitTeam?team_id=' +
																						this
																						.teamDetail
																						.id
																				})
																			} else {
																				if (auth
																					.bill_id &&
																					(auth
																						.user_stay_status ==
																						1
																					)
																				) {
																					this.haveRoom =
																						true
																				}
																			}
														
																		})
																}).catch(err => {
																	if (err.includes(
																			'清洁')) {
																		uni.showModal({
																			title: '提示',
																			content: '当前是脏房、是否更换其他同房型其他干净房',
																			cancelText: '等待清洁',
																			confirmText: '更换房间',
																			success: res => {
																				if (res
																					.confirm
																				) {
																					// 更换房间
																					this.$iBox
																						.http(
																							'changeRoomCheckIn', {
																								teamId: this
																									.team_id
																							}
																						)
																						({
																							method: 'post'
																						})
																						.then(
																							res => {
																								uni.reLaunch({
																									url: '/pages/myRoom/myRoom'
																								})
																							}
																						)
																				} else {
														
																				}
																			}
																		})
																	}
														
														
																})
														}
													}else {
														if (this.teamDetail
															.dirty_check_in == 1 && room &&
															room.clean_status == 0) {
															uni.showModal({
																title: '提示',
																content: '当前房间是脏房,是否继续入住',
																cancelText: '继续入住',
																confirmText: '等待清洁',
																success: res => {
																	if (res
																		.confirm
																	) {
																		
																	} else {
																		this.$iBox
																			.http(
																				'teamCheckIn', {
																					teamId: this
																						.team_id
																				}
																			)
																			({
																				method: 'post'
																			})
																			.then(
																				res => {
																					uni.reLaunch({
																						url: '/pages/myRoom/myRoom'
																					})
																				}
																			)
																	}
																}
															})
														
														
														} else {
															this.$iBox
																.http('teamCheckIn', {
																	teamId: this
																		.team_id
																})({
																	method: 'post'
																})
																.then(res => {
																	this.$iBox.http(
																		'getTeamRoomBillInfo',
																		this
																		.params)({
																		method: 'post'
																	}).then(
																		res => {
																			let auth =
																				res
																				.data
																				.users
																				.filter(
																					item => {
																						return item
																							.common_code ==
																							this
																							.userInfo
																							.common_code
																					}
																				)[
																					0
																				]
														
																			if (auth
																				.team_confirm ==
																				0
																			) {
																				uni.navigateTo({
																					url: '/packageB/teamCheckIn/waitTeam?team_id=' +
																						this
																						.teamDetail
																						.id
																				})
																			} else {
																				if (auth
																					.bill_id &&
																					(auth
																						.user_stay_status ==
																						1
																					)
																				) {
																					this.haveRoom =
																						true
																				}
																			}
														
																		})
																}).catch(err => {
																	if (err.includes(
																			'清洁')) {
																		uni.showModal({
																			title: '提示',
																			content: '当前是脏房、请等待房间清洁完成',
																			showCancel:false,
																			confirmText: '等待清洁',
																			success: res => {
																				
																			}
																		})
																	}
														
														
																})
														}
													}
													

												} else {
													this.showDevice = true;
												}
											})
											.catch(error => {
												console.error('签到前检查失败:', error);
												this.showDevice = true;
											});



									}
								})

							})
						}

					}
				})

			},
			loginSucess() {
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					let set = this.setting.filter(item => {
						return item.sign == 'auto_register_member'
					})
					if (set[0].property) {
						let a = set[0].property.value
						if (a == 2) {
							if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
								.upgrade_growth_value > -1) {
								this.if_login = false
								// 轮询查询订单
								let self = this
								this.timeInfo && clearInterval(this.timeInfo)
								this.timeInfo = setInterval((function target() {
									self.scanExc()
									return target
								})(), 5000)
							} else {
								this.if_login = true
							}

						} else if (a == 1) {
							// this.pop = true
							if (this.userInfo.phone) {
								this.if_login = false
								// 轮询查询订单
								let self = this
								this.timeInfo && clearInterval(this.timeInfo)
								this.timeInfo = setInterval((function target() {
									self.scanExc()
									return target
								})(), 5000)
							} else {
								this.if_login = true
							}
						}
					}

				})
			},

			toCloseLogin() {
				uni.showModal({
					title: '提示！',
					content: '为了获得更完整的会员服务请您授权您的手机号！',
					showCancel: false,
					success: res => {
						this.hackReset = false
						this.$nextTick(() => {
							this.hackReset = true
						})
						this.if_login = true
					}
				})
			},
			toTakePhone() {
				console.log('dsdsd');
				wx.chooseMedia({
					count: 9,
					mediaType: ['image'],
					sourceType: ['album', 'camera'],
					sizeType: ['compressed'],
					camera: 'back',
					success: (res) => {
						console.log(res, 'id');
						this.popLoading = true
						uni.uploadFile({
							url: this.action_idCard, //
							header: {
								'AUTHTOKEN': this.userInfo.user_token,
								'Content-Type': 'application/x-www-form-urlencoded',
								'chartset': 'utf-8'
							},
							filePath: res.tempFiles[0].tempFilePath,
							name: 'file',
							formData: {
								'shop_id': this.hotel.id,
								'team_id': this.teamDetail.id
							},
							success: (uploadFileRes) => {
								if (JSON.parse(uploadFileRes.data).data) {
									let dataInfo = JSON.parse(uploadFileRes.data).data
									this.idCard_img = dataInfo.id_image
									this.name = dataInfo.name
									this.id_number = dataInfo.identification_number
									this.popLoading = false


								} else {
									uni.showModal({
										title: '提示',
										content: '身份证照片识别失败，请重新上传身份证！',
										showCancel: false,
										success: res1 => {
											this.popLoading = false
											this.idCard_img = ''
											this.name = ''
											this.id_number = ''
										}
									})
								}

							},
							fail: () => {
								this.popLoading = false
							}
						});
						console.log(res.tempFiles)
					},
					fail: err => {
						console.log(err);
						this.popLoading = false
					}
				})
			},
			chooseRoom(e) {
				this.chooseId = e.id
			},

			upInfo() {
				if (this.totalPrice > 0) {
					this.popPay = true
					console.log('dayu1');
				} else {
					console.log('dayu2')
					uni.navigateTo({
						url: '/packageB/teamCheckIn/confirmTeam?team_id=' + this
							.teamDetail.id + '&share_code=' + this.share_code + '&startTime=' + this.teamDetail
							.enter_time_plan + '&endTime=' + this.teamDetail.leave_time_plan
					})

				}
			},
			upThrottle(e) {
				// 判断等待入住人二次确认

				//查询订单信息
				this.$iBox
					.http('getTeamRoomBillInfo', this.params)({
						method: 'post'
					})
					.then(res => {
						this.userList = res.data.users
						let auth = this.userList.filter(item => {
							return item.common_code == this.userInfo.common_code
						})[0]
						// 1等待二次确认不能点击，2被拒绝不能点击

						if (this.userList.length == 2 && auth.share_from == 0 && auth.confirm == 0) {
							console.log(1);
							uni.showToast({
								icon: 'none',
								title: '请等待确认弹窗!'
							})


						} else if (this.userList.length == 2 && auth.share_from == 0 && auth.confirm == 1) {
							uni.showToast({
								icon: 'none',
								title: '请等待主入住人二次确认！'
							})
						} else if (this.userList.length == 1 && auth.share_from == 0) {
							this.$iBox.http('getQrInfo', {
									code: this.share_code
								})({
									method: 'post'
								})
								.then(res => {
									if (res.data) {
										console.log(auth.share_code, 'auth.share_code');
										//status0,未点击|1，点击过|2,过期|3被分享人拒绝，4分享人拒绝，被分享人拒绝，需要给提示，并关闭小程序提示现场扫码
										if (res.data.status == 4 || res.data.status == 2) {
											this.elseNoShare = true
										} else if (res.data.status == 2) {
											this.noDetail = true
										}
									}
								})
						} else {

							this.$iBox.throttle1(() => {
								this.upInfo()
							}, 2000);

						}
					})

			},
			payFor(e) {
				this.$iBox.throttle(() => {
					this.fnPay(e)
				}, 2000);
			},
			fnPay(e) {
				uni.showLoading({
					title: '等待支付...'
				})
				console.log(this.totalPrice, 'xufang');
				this.payType = e
				let params = {
					team_id: this.teamDetail.id,
					pay_type: ''
				}

				if (e == 'weixin') {
					params.pay_type = 1
					this.$iBox
						.http('payTeamRoomAmount', params)({
							method: 'post'
						})
						.then(res => {
							if (res.data.bizCode == '0000') {
								// 随行付
								uni.requestPayment({
									provider: 'wxpay',
									AppId: res.data.payAppId,
									timeStamp: res.data.payTimeStamp,
									nonceStr: res.data.paynonceStr,
									package: res.data.payPackage,
									signType: res.data.paySignType,
									paySign: res.data.paySign,
									success: (res) => {
										uni.hideLoading()
										this.popPay = false

										uni.navigateTo({
											url: '/packageB/teamCheckIn/confirmTeam?team_id=' +
												this.teamDetail.id + '&startTime=' + this
												.teamDetail.enter_time_plan + '&endTime=' +
												this.teamDetail.leave_time_plan
										})

									},
									fail: function(err) {
										uni.hideLoading()
									}
								});


							} else {
								// 微信支付
								uni.requestPayment({
									provider: 'wxpay',
									timeStamp: res.data.timeStamp,
									nonceStr: res.data.nonceStr,
									package: res.data.package,
									signType: 'MD5',
									paySign: res.data.paySign,
									success: (res) => {
										uni.hideLoading()
										this.popPay = false
										uni.navigateTo({
											url: '/packageB/teamCheckIn/confirmTeam?team_id=' +
												this.teamDetail.id + '&startTime=' + this
												.teamDetail.enter_time_plan + '&endTime=' +
												this.teamDetail.leave_time_plan
										})

									},
									fail: function(err) {
										uni.hideLoading()
									}
								});
							}


						}).catch(err => {
							console.log(err, 'err');

						})
				} else {
					params.pay_type = e == 'tongyong' ? 2 : (e == 'duli' ? 3 : '')

					this.$iBox
						.http('payTeamRoomAmount', params)({
							method: 'post'
						})
						.then(res => {
							uni.hideLoading()
							this.popPay = false
							// 支付成功提示!
							uni.showModal({
								title: '支付提示',
								content: '支付成功!',
								showCancel: false,
								success: res => {
									if (res.confirm) {
										uni.navigateTo({
											url: '/packageB/teamCheckIn/confirmTeam?team_id=' +
												this.teamDetail.id + '&startTime=' + this
												.teamDetail.enter_time_plan + '&endTime=' +
												this.teamDetail.leave_time_plan
										})
									}
								}
							})
						}).catch(err => {
							console.log(err, 'err');
						})
				}

			},
			closePop() {
				this.popPay = false
			},

			// 重新搜索设备
			reload() {
				uni.showLoading({
					title: '正在扫描U盾中....'
				})
				this.stopBluetoothDevicesDiscovery()
				wx.closeBluetoothAdapter({
					success(res) {
						uni.hideLoading()
					},
					fail(res) {
						uni.hideLoading()
					}
				})
				this.blueGet().then(found => {

					if (!found) {
						uni.hideLoading()
						uni.showToast({
							icon: "none",
							title: "未找到可用设备，请检查蓝牙设置",
							duration: 3000
						});
					}
				});
			},

			////////////////////////=============================蓝牙U Key盾检测==========================
			peiDui() {
				return new Promise((resolve) => {
					let self = this;
					if(self.blueDeviceSetting){
						uni.showLoading({
						title: '正在扫描U盾中...'
					})
					uni.openBluetoothAdapter({
						success: (e) => {
							uni.hideLoading()
							console.log('初始化蓝牙成功:', e.errMsg);
							this.startBluetoothDeviceDiscovery(resolve);
						},
						fail: (e) => {
							uni.hideLoading()
							uni.showToast({
								icon: "none",
								title: "请先打开手机蓝牙",
								duration: 3000
							});
							resolve(false); // 初始化失败
						}
					});
					}else{
						resolve(true)
					}
					
				});
			},

			// 开始搜索蓝牙设备（接收Promise回调）
			startBluetoothDeviceDiscovery(resolve) {
				let self = this;
				this.searchTimeout = setTimeout(() => { // 设置搜索超时（10秒）
					uni.hideLoading()
					self.handleSearchTimeout(resolve);
				}, 20000);
				console.log('开启');
				uni.startBluetoothDevicesDiscovery({
					allowDuplicatesKey: false,
					success: (res) => {
						self.onBluetoothDeviceFound(resolve); // 监听设备发现事件
					},
					fail: (res) => {
						console.log(res);
						uni.hideLoading()
						clearTimeout(self.searchTimeout);
						uni.showToast({
							icon: "none",
							title: "搜索设备失败",
							duration: 3000
						});
						resolve(false);
					}
				});
			},
			// 处理设备发现事件（核心匹配逻辑）
			onBluetoothDeviceFound(resolve) {
				let self = this;
				uni.onBluetoothDeviceFound((devices) => {

					let foundValidDevice = false;
					console.log(devices, 'devices');
					devices.devices.forEach(device => {
						// 提取MAC地址（兼容不同设备命名格式）
						const dev = device.localName.slice(-12);

						// 每两个字符分组并用冒号连接
						const mac = dev.match(/.{1,2}/g)?.join(':') || '';
						console.log(mac, "mac1");
						// 检查是否为目标设备
						const targetDevice = this.blueList.find(item => item.mac === mac);
						console.log(targetDevice, "目标");
						if (targetDevice) {
							foundValidDevice = true;
							// 检查信号强度（RSSI）是否符合要求
							const rssiValid = targetDevice.max_value === 0 ||
								device.RSSI > targetDevice.max_value;
							console.log(rssiValid, '强度');
							if (rssiValid) {
								this.handleDeviceFound(resolve, device);
							}
						}
					});

					// 未找到有效设备时保持搜索
					if (!foundValidDevice) {
						this.showDevice = true; // 显示搜索中状态
					}
				});

			},

			// 提取MAC地址工具方法（兼容常见命名格式）
			extractMacAddress(localName) {
				if (!localName) return null;
				// 匹配类似"ABC-1234-5678"或"AB:CD:EF:12:34:56"的格式
				const macMatch = localName.match(/([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})/);
				console.log(macMatch[0].replace(/-/g, ':'), '匹配demac');
				return macMatch ? macMatch[0].replace(/-/g, ':') : null;
			},

			// 设备找到后的处理
			handleDeviceFound(resolve, device) {
				uni.hideLoading()
				this.showDevice = false; // 隐藏搜索状态
				this.stopBluetoothDevicesDiscovery(); // 停止搜索

				// 可选：记录当前连接设备信息
				this.currentConnectedDevice = device;

				uni.closeBluetoothAdapter({
					success: () => {
						console.log('蓝牙适配器已关闭');
						resolve(true); // 通知搜索成功
					},
					fail: (err) => {
						console.warn('关闭蓝牙适配器失败:', err);
						resolve(true); // 不影响主流程继续
					}
				});
			},

			// 处理搜索超时
			handleSearchTimeout(resolve) {
				uni.hideLoading()
				// 显示搜索超时状态
				clearTimeout(this.searchTimeout);
				uni.stopBluetoothDevicesDiscovery({
					complete: () => resolve(false) // 通知搜索失败
				});
			},

			// 停止搜索蓝牙设备
			stopBluetoothDevicesDiscovery() {
				uni.hideLoading()
				uni.stopBluetoothDevicesDiscovery({
					complete: (res) => console.log('停止搜索结果:', res)
				});
				uni.offBluetoothDeviceFound(); // 移除监听防止重复触发
			},


		},
		onUnload() {
			clearInterval(this.timeInfo)
			clearInterval(this.timeInfo1)
		},
		async onShareAppMessage(res) {
			// 调用异步函数获取分享数据
			uni.showLoading({
				title: '即将分享...'
			})
			try {

				this.type = 1
				this.shareShow = false
				this.againSure = false
				const shareData = await new Promise((resolve, reject) => {
					this.$iBox.http('getSameRoomCode', {
						team_id: this.team_id
					})({
						method: 'post'
					}).then(res => {
						this.share_code = res.data.code
						// this.team_id = res.data.team_id
						this.img_src = res.data.url

						//查询订单信息
						this.$iBox
							.http(
								'getTeamRoomBillInfo',
								this.params)({
								method: 'post'
							})
							.then(res => {
								this.teamDetail = res.data
								let auth = res.data.users.filter(
									item => {
										return item.common_code == this.userInfo.common_code
									})[0]
								if (auth) {
									this.user = auth
									this.ifAuth = auth.authentication
								}
							})

						uni.hideLoading()
						resolve(res)
					}).catch(err => {
						uni.showToast({
							icon: 'none',
							title: this.team_id + '分享失败请重试!'
						})
					})
				})
				console.log(shareData, 'shareData');
				let url = ''
				// 1.返回节点对象
				let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
				let currentPage = pages[pages.length - 1]; //获取当前页面的对象
				url = currentPage.route //当前页面url
				return {
					title: `${this.teamDetail.team_name}-${this.name}分享给您同住!`,
					path: url + '?teamId=' + shareData.data.team_id + '&code=' + shareData.data.code,
					imageUrl: this.hotel.pic_list[0]
				};


			} catch (error) {
				// 处理异步函数中可能出现的错误
				console.error('获取分享数据失败:', error);
				// 返回默认的转发信息
				return {
					title: '默认分享标题',
					path: '/pages/index/index'
				};
			}

		},
	}
</script>

<style scoped lang="scss">
	.custom-button {
		background-color: #007BFF;
		/* 蓝色背景 */
		color: #FFFFFF;
		/* 白色字体 */
		padding: 10px 20px;
		/* 按钮内边距 */
		border-radius: 5px;
		/* 圆角 */
		text-align: center;
		/* 文本居中 */
		font-size: 16px;
		/* 字体大小 */
		cursor: pointer;
		/* 鼠标样式 */
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
		/* 添加阴影效果 */
	}

	.custom-button:active {
		background-color: #0056b3;
		/* 点击时背景颜色变深 */
	}

	.Scanbox {
		height: 65vh;
		margin-top: 30rpx;
		padding: 20rpx;
		border-radius: 30rpx;
		background-color: #FFFFFF;
	}

	.Scanbox::before {
		position: absolute;
		left: 12rpx;
		top: 20vh;
		width: 16px;
		height: 16px;
		background-color: #555555;
		border-radius: 50%;
		content: "";
	}

	.Scanbox::after {
		position: absolute;
		right: 12rpx;
		top: 20vh;
		width: 16px;
		height: 16px;
		background-color: #555555;
		border-radius: 50%;
		content: "";
	}

	.Scanbox1 {
		height: 65vh;
		margin-top: 30rpx;
		padding: 20rpx;
		border-radius: 30rpx;
		background-color: #FFFFFF;
	}

	.Scanbox1::before {
		position: absolute;
		left: 12rpx;
		top: 30vh;
		width: 16px;
		height: 16px;
		background-color: #555555;
		border-radius: 50%;
		content: "";
	}

	.Scanbox1::after {
		position: absolute;
		right: 12rpx;
		top: 30vh;
		width: 16px;
		height: 16px;
		background-color: #555555;
		border-radius: 50%;
		content: "";
	}

	// .sharebtn {

	// 	width: 400rpx;
	// 	height: 60rpx;
	// 	display: flex;
	// 	align-items: center;
	// 	justify-content: space-between;
	// 	// background-color: inherit;
	// 	padding: 0;
	// 	margin: 0;

	// 	&::after {
	// 		border: none;
	// 	}
	// }

	.sharebtn {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 100%;
		height: 40%;
		opacity: 0;
		z-index: 99999;
	}

	.deviceBox {
		height: 50vh;
		width: 700rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.deBtn1 {
			width: 250rpx;
			height: 80rpx;
			border-radius: 40rpx;
			border: 1px solid #545454;
			color: #545454;
			// margin-top: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.deBtn {
			width: 600rpx;
			height: 80rpx;
			border-radius: 40rpx;

			color: #FFFFFF;
			margin-top: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.stepBox {
		width: 100%;
		height: 160rpx;
		background-color: #FFFFFF;
		padding: 20rpx 0;
	}

	.btnSure {
		margin: 20rpx auto;
		display: flex;
		align-content: center;
		justify-content: center;
		width: 660rpx;
		padding: 20rpx;
		border-radius: 30rpx;
		// background-color: #00aa7f;
		color: #FFFFFF;
	}

	.teamBox {
		padding: 10rpx 30rpx;
		color: #333333;
		width: 100%;
		background-color: #FFFFFF;
		margin-top: 10rpx;

		.content {
			width: 100%;
			height: 270rpx;
			// margin: 20rpx auto;
			border-radius: 20rpx;

			padding: 20rpx 30rpx;

			.title {
				font-size: 36rpx;
				font-weight: 600;
				display: flex;
				justify-content: space-between;
				height: 25%;
				align-items: center;
				margin-top: 10rpx;
			}
		}

	}

	.idCardBox {

		margin: 10rpx auto;

		width: 100%;
		background-color: #FFFFFF;

		.title {
			font-size: 30rpx;
			font-weight: 300;
			display: flex;
			align-items: center;
			// justify-content: center;
			background: #FFFFFF;
			padding: 20rpx 30rpx;
		}

		.picBox {
			border-right: 1px dashed #bbbcbd;
			border-bottom: 1px dashed #bbbcbd;
			border-left: 1px dashed #bbbcbd;
			width: 100%;
			// min-height: 600rpx;
			overflow: auto;
			background-color: #FFFFFF;

			&_content {
				height: 300rpx;
				width: 500rpx;
				margin: 2rpx auto;
				border-radius: 16rpx;
				background: #f3f4f6;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
			}

			.takePhoto {
				position: absolute;
				z-index: 3;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				margin: auto;
				width: 120rpx;
				height: 120rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
			}

			.msgBox {
				padding: 10rpx 30rpx;
				font-size: 36rpx;

				color: #303133;

				.name {
					font-size: 30rpx;
					padding: 20rpx 0;
					border-bottom: 1px solid #e4e7ed;
					display: flex;
					align-items: center;

				}

				.id_number {
					padding: 20rpx 0;
					font-size: 30rpx;
					border-bottom: 1px solid #e4e7ed;
					display: flex;
					align-items: center;

				}
			}
		}



	}

	.roomBox {
		margin: 50rpx auto;
		width: 1005;

		padding: 20rpx;
		background: #ffffff;

		.title {
			font-size: 40rpx;
			font-weight: 500;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #ffffff;
			padding: 20rpx 30rpx;
		}

		.roomList {
			display: flex;

			.room {
				display: flex;
				flex-wrap: wrap;
				justify-content: center;
				align-items: center;
				width: 220rpx;
				height: 220rpx;
				position: relative;

				.item {
					width: 200rpx;
					height: 200rpx;
					padding: 10rpx;
					background-color: #00aa7f;
					border-radius: 4rpx;
					display: flex;
					flex-direction: column;
				}

			}
		}

	}

	.cashBox {
		width: 100%;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		margin: 20rpx auto;
		border-radius: 20rpx;
		padding: 0rpx 30rpx;

		.room {
			padding: 30rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1px solid #e4e7ed;

		}
	}

	.shareBox {
		width: 100%;
		height: 500rpx;
		border-radius: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;
		justify-content: space-around;
		position: relative;
	}

	.timeBox {
		width: 120rpx;
		height: 120rpx;
		background-color: rgba(90, 90, 90, 0.2);
		backdrop-filter: blur(22.5px);
		-webkit-backdrop-filter: blur(22.5px);
		border: 0.666667px solid rgba(255, 255, 255, 0.18);
		box-shadow: rgba(70, 70, 70, 0.2) 0px 6px 15px 0px;
		-webkit-box-shadow: rgba(89, 89, 89, 0.2) 0px 6px 15px 0px;
		border-radius: 12rpx;
		-webkit-border-radius: 12px;
		color: rgba(0, 0, 0, 1);
		margin: 0 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	@keyframes xing {
		0% {
			transform: scale(1);
		}

		25% {
			transform: scale(1.05);
		}

		50% {
			transform: scale(1);
		}

		75% {
			transform: scale(1.05);
		}
	}

	.primary-button {
		font-family: 'Ropa Sans', sans-serif;
		/* font-family: 'Valorant', sans-serif; */
		color: white;
		cursor: pointer;
		font-size: 13px;
		font-weight: bold;
		letter-spacing: 0.05rem;
		border: 1px solid #0E1822;
		padding: 14rpx 200rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 531.28 200'%3E%3Cdefs%3E%3Cstyle%3E .shape %7B fill: %23FF4655 /* fill: %230E1822; */ %7D %3C/style%3E%3C/defs%3E%3Cg id='Layer_2' data-name='Layer 2'%3E%3Cg id='Layer_1-2' data-name='Layer 1'%3E%3Cpolygon class='shape' points='415.81 200 0 200 115.47 0 531.28 0 415.81 200' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E%0A");
		// background-color: #FF4655;
		background-size: 200%;
		background-position: 200%;
		background-repeat: no-repeat;
		transition: 0.3s ease-in-out;
		transition-property: background-position, border, color;
		position: relative;
		z-index: 1;
		-webkit-animation-name: xing;
		-webkit-animation-timing-function: ease-in-out;
		-webkit-animation-iteration-count: infinite;
		-webkit-animation-duration: 2s;
	}

	.primary-button:before {
		content: "";
		position: absolute;
		background-color: #FF4655;
		width: 0.3rem;
		height: 0.3rem;
		bottom: -1px;
		right: -1px;
		transition: background-color 0.15s ease-in-out;
	}

	.primary-button:hover:before {
		background-color: white;
	}

	.primary-button:hover:after {
		background-color: white;
	}

	.primary-button:after {
		content: "";
		position: absolute;
		background-color: #FF4655;
		width: 0.3rem;
		height: 0.3rem;
		bottom: -1px;
		right: -1px;
		transition: background-color 0.15s ease-in-out;
	}

	.button-borders {
		position: relative;
		width: fit-content;
		height: fit-content;
	}

	.button-borders:before {
		content: "";
		position: absolute;
		width: calc(100% + 0.5em);
		height: 50%;
		left: -0.3em;
		top: -0.3em;
		border: 1px solid #0E1822;
		border-bottom: 0px;
		/* opacity: 0.3; */
	}

	.button-borders:after {
		content: "";
		position: absolute;
		width: calc(100% + 0.5em);
		height: 50%;
		left: -0.3em;
		bottom: -0.3em;
		border: 1px solid #0E1822;
		border-top: 0px;
		/* opacity: 0.3; */
		z-index: 0;
	}

	.shape {
		fill: #0E1822;
	}

	@keyframes xing {
		0% {
			transform: scale(1);
		}

		25% {
			transform: scale(1.14);
		}

		50% {
			transform: scale(1);
		}

		75% {
			transform: scale(1.14);
		}
	}

	.loading,
	.loading>view {
		position: relative;
		box-sizing: border-box;
	}

	.loading {
		display: block;
		font-size: 0;
		color: #000;
	}

	.loading.la-dark {
		color: #333;
	}

	.loading>view {
		display: inline-block;
		float: none;
		background-color: currentColor;
		border: 0 solid currentColor;
	}

	.loading {
		width: 54px;
		height: 18px;
	}

	.loading>view:nth-child(1) {
		animation-delay: -200ms;
	}

	.loading>view:nth-child(2) {
		animation-delay: -100ms;
	}

	.loading>view:nth-child(3) {
		animation-delay: 0ms;
	}

	.loading>view {
		width: 7px;
		height: 7px;
		margin: 4px;
		border-radius: 100%;
		animation: ball-pulse 1s ease infinite;
	}

	.loading.la-sm {
		width: 26px;
		height: 8px;
	}

	.loading.la-sm>view {
		width: 4px;
		height: 4px;
		margin: 2px;
	}

	.loading.la-2x {
		width: 108px;
		height: 36px;
	}

	.loading.la-2x>view {
		width: 20px;
		height: 20px;
		margin: 8px;
	}

	.loading.la-3x {
		width: 162px;
		height: 54px;
	}

	.loading.la-3x>view {
		width: 30px;
		height: 30px;
		margin: 12px;
	}

	@keyframes ball-pulse {

		0%,
		60%,
		100% {
			opacity: 1;
			transform: scale(1);
		}

		30% {
			opacity: 0.1;
			transform: scale(0.01);
		}
	}
</style>